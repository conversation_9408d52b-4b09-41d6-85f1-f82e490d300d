#!/usr/bin/env python3
"""
测试电梯ID传参修复的脚本
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json
import time

class ElevatorIDTestNode(Node):
    def __init__(self):
        super().__init__('elevator_id_test_node')
        
        # 创建发布器
        self.task_publisher = self.create_publisher(
            String, 
            '/behaviour_module/task_requests', 
            10
        )
        
        # 创建状态订阅器
        self.status_subscription = self.create_subscription(
            String,
            '/behaviour_module/module_status',
            self.status_callback,
            10
        )
        
        self.get_logger().info("电梯ID测试节点已启动")
        
        # 等待一秒让发布器建立连接
        time.sleep(1.0)
        
        # 发送测试任务
        self.send_test_tasks()
    
    def status_callback(self, msg):
        """监听模块状态"""
        self.get_logger().info(f"模块状态: {msg.data}")
    
    def send_test_tasks(self):
        """发送测试任务"""
        
        # 测试1: 使用客梯ID的进入电梯任务
        self.get_logger().info("=== 测试1: 进入电梯模块 - 客梯ID ===")
        enter_task = {
            "task_id": "enter_test_client_elevator",
            "building_id": "building_1",
            "object_type": "package",
            "current_floor": 1,
            "target_floor": 2,
            "object_weight": 2.5,
            "priority": "high",
            "special_requirements": "",
            "execution_mode": "api",
            "elevator_id": "48b02df90a3e",  # 客梯ID
            "elevator_call_timeout": 45.0,
            "door_wait_timeout": 90.0,
            "pickup_pose": {
                "position": {"x": 1.0, "y": 2.0, "z": 0.0},
                "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}
            },
            "delivery_pose": {
                "position": {"x": 3.0, "y": 4.0, "z": 0.0},
                "orientation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}
            }
        }
        
        self.publish_task(enter_task)
        time.sleep(5.0)  # 等待5秒
        
        # 测试2: 使用货梯ID的进入电梯任务
        self.get_logger().info("=== 测试2: 进入电梯模块 - 货梯ID ===")
        enter_task_freight = enter_task.copy()
        enter_task_freight["task_id"] = "enter_test_freight_elevator"
        enter_task_freight["elevator_id"] = "3c6d66039b10"  # 货梯ID
        
        self.publish_task(enter_task_freight)
        time.sleep(5.0)  # 等待5秒
        
        # 测试3: 使用客梯ID的退出电梯任务
        self.get_logger().info("=== 测试3: 退出电梯模块 - 客梯ID ===")
        exit_task = enter_task.copy()
        exit_task["task_id"] = "exit_test_client_elevator"
        exit_task["elevator_id"] = "48b02df90a3e"  # 客梯ID
        
        self.publish_task(exit_task)
        time.sleep(5.0)  # 等待5秒
        
        # 测试4: 不提供电梯ID的任务（应该使用默认值）
        self.get_logger().info("=== 测试4: 进入电梯模块 - 无电梯ID（使用默认值） ===")
        no_id_task = enter_task.copy()
        no_id_task["task_id"] = "enter_test_no_elevator_id"
        del no_id_task["elevator_id"]  # 删除电梯ID
        
        self.publish_task(no_id_task)
        time.sleep(5.0)  # 等待5秒
        
        # 测试5: API模式但没有电梯ID（应该报错）
        self.get_logger().info("=== 测试5: API模式无电梯ID（应该报错） ===")
        error_task = enter_task.copy()
        error_task["task_id"] = "enter_test_api_no_id"
        error_task["elevator_id"] = ""  # 空电梯ID
        
        self.publish_task(error_task)
        
        self.get_logger().info("所有测试任务已发送完成")
    
    def publish_task(self, task_dict):
        """发布任务"""
        msg = String()
        msg.data = json.dumps(task_dict)
        self.task_publisher.publish(msg)
        self.get_logger().info(f"发送任务: {task_dict['task_id']}, 电梯ID: {task_dict.get('elevator_id', '未设置')}")

def main(args=None):
    rclpy.init(args=args)
    
    try:
        test_node = ElevatorIDTestNode()
        
        # 运行30秒来观察结果
        rclpy.spin_until_future_complete(
            test_node, 
            test_node.create_future(), 
            timeout_sec=30.0
        )
        
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"测试节点运行失败: {e}")
    finally:
        rclpy.shutdown()

if __name__ == '__main__':
    main()

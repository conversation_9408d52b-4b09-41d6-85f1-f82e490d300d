#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.substitutions import Command, FindExecutable, LaunchConfiguration, PathJoinSubstitution
from launch.event_handlers import OnProcessStart
from launch.actions import ExecuteProcess, RegisterEventHandler, DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch_ros.parameter_descriptions import ParameterValue
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from launch.launch_description_sources import PythonLaunchDescriptionSource
import xacro

def generate_launch_description():
    # Get package directories
    ackermann_gazebo_dir = get_package_share_directory("ackermann_gazebo")
    mapping_nodes_dir = get_package_share_directory("mapping_nodes")

    # File paths
    urdf_path = os.path.join(ackermann_gazebo_dir, "urdf", "sim.urdf.xacro")
    world_path = os.path.join(ackermann_gazebo_dir, "worlds", "simple_world.world")
    rviz_config_path = os.path.join(ackermann_gazebo_dir, "rviz", "simulation.rviz")
    local_mapping_config_path = os.path.join(mapping_nodes_dir, "config", "local_mapping_params.yaml")

    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )

    enable_rviz_arg = DeclareLaunchArgument(
        'enable_rviz',
        default_value='true',
        description='Enable RViz2'
    )

    enable_local_mapping_arg = DeclareLaunchArgument(
        'enable_local_mapping',
        default_value='true',
        description='Enable local mapping'
    )

    # Launch configurations
    use_sim_time = LaunchConfiguration('use_sim_time')
    enable_rviz = LaunchConfiguration('enable_rviz')
    enable_local_mapping = LaunchConfiguration('enable_local_mapping')

    # Robot description
    robot_description = ParameterValue(
        Command([FindExecutable(name='xacro'), ' ', urdf_path]),
        value_type=str
    )

    # Robot state publisher
    robot_state_publisher_node = Node(
        package="robot_state_publisher",
        executable="robot_state_publisher",
        name="robot_state_publisher",
        output="screen",
        parameters=[
            {
                "use_sim_time": use_sim_time,
                "robot_description": robot_description,
            }
        ],
    )

    # Gazebo server
    gazebo = ExecuteProcess(
        cmd=['gzserver', '--verbose', world_path, '-s', 'libgazebo_ros_factory.so'],
        output='screen'
    )

    # Gazebo client
    gzclient = ExecuteProcess(
        cmd=['gzclient'],
        output='screen'
    )

    # Spawn robot
    spawn_robot = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-topic', 'robot_description',
            '-entity', 'bot_ackermann',
            '-x', '1.0',
            '-y', '0.0',
            '-z', '0.1'
        ],
        output='screen'
    )

    # LaserScan to PointCloud converter
    laserscan_to_pointcloud_node = Node(
        package='ackermann_gazebo',
        executable='laserscan_to_pointcloud_node',
        name='laserscan_to_pointcloud',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'target_frame': 'agv_base_link',  # Changed to agv_base_link
                'scan_topic': '/scan',
                'pointcloud_topic': '/livox/point_cloud'
            }
        ]
    )

    # RViz2
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_path],
        output='screen',
        parameters=[{'use_sim_time': use_sim_time}],
        condition=IfCondition(LaunchConfiguration('enable_rviz'))
    )

    # Local mapping node
    local_mapping_node = Node(
        package='mapping_nodes',
        executable='local_mapping_node',
        name='local_mapping',
        output='screen',
        parameters=[
            local_mapping_config_path,  # Load from config file
            {
                'use_sim_time': use_sim_time,
                # Override specific parameters for simulation
                'map_size': 10.0,  # 10x10 meters for simulation
                'odometry_topic': '/odom',  # Use simulation odometry topic
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_local_mapping'))
    )

    # Event handler to spawn robot after Gazebo starts
    spawn_event = RegisterEventHandler(
        event_handler=OnProcessStart(
            target_action=gazebo,
            on_start=[spawn_robot]
        )
    )

    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        enable_rviz_arg,
        enable_local_mapping_arg,

        # Core simulation nodes
        gazebo,
        gzclient,
        robot_state_publisher_node,
        spawn_event,

        # Sensor processing
        laserscan_to_pointcloud_node,

        # Visualization and mapping
        rviz_node,
        local_mapping_node,
    ])

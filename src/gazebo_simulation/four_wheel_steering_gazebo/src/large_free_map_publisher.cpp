#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <chrono>

class LargeFreeMapPublisher : public rclcpp::Node
{
public:
    LargeFreeMapPublisher() : Node("large_free_map_publisher")
    {
        // Create publisher for global map with TRANSIENT_LOCAL QoS
        // This ensures compatibility with costmap_generator and allows late-joining subscribers
        rclcpp::QoS map_qos(10);
        map_qos.transient_local();  // Allow late-joining subscribers to receive previous messages
        map_qos.reliable();         // Ensure reliable delivery
        
        map_publisher_ = this->create_publisher<nav_msgs::msg::OccupancyGrid>("/global_map", map_qos);
        
        // Create timer to publish map periodically
        timer_ = this->create_wall_timer(
            std::chrono::seconds(1),
            std::bind(&LargeFreeMapPublisher::publish_map, this));
        
        // Initialize the large free map
        initialize_map();
        
        RCLCPP_INFO(this->get_logger(), "Large free map publisher started");
        RCLCPP_INFO(this->get_logger(), "Map size: %dx%d pixels (%.1fm x %.1fm)", 
                    map_width_, map_height_, 
                    map_width_ * map_resolution_, map_height_ * map_resolution_);
    }

private:
    void initialize_map()
    {
        // Map parameters
        map_width_ = 2000;      // 2000 pixels
        map_height_ = 2000;     // 2000 pixels  
        map_resolution_ = 0.05; // 0.05 m/pixel = 100m x 100m total
        
        // Create occupancy grid message
        map_msg_.header.frame_id = "map";
        map_msg_.info.resolution = map_resolution_;
        map_msg_.info.width = map_width_;
        map_msg_.info.height = map_height_;
        
        // Set origin at center of map
        map_msg_.info.origin.position.x = -map_width_ * map_resolution_ / 2.0;  // -50.0
        map_msg_.info.origin.position.y = -map_height_ * map_resolution_ / 2.0; // -50.0
        map_msg_.info.origin.position.z = 0.0;
        map_msg_.info.origin.orientation.w = 1.0;
        
        // Initialize all cells as free space (0 = free in OccupancyGrid)
        map_msg_.data.resize(map_width_ * map_height_);
        std::fill(map_msg_.data.begin(), map_msg_.data.end(), 0);  // 0 = free space
        
        RCLCPP_INFO(this->get_logger(), "Initialized large free map:");
        RCLCPP_INFO(this->get_logger(), "  Size: %dx%d pixels", map_width_, map_height_);
        RCLCPP_INFO(this->get_logger(), "  Resolution: %.3f m/pixel", map_resolution_);
        RCLCPP_INFO(this->get_logger(), "  Physical size: %.1fm x %.1fm", 
                    map_width_ * map_resolution_, map_height_ * map_resolution_);
        RCLCPP_INFO(this->get_logger(), "  Origin: (%.1f, %.1f)", 
                    map_msg_.info.origin.position.x, map_msg_.info.origin.position.y);
        RCLCPP_INFO(this->get_logger(), "  All cells are FREE SPACE");
    }
    
    void publish_map()
    {
        // Update timestamp
        map_msg_.header.stamp = this->get_clock()->now();
        
        // Publish the map
        map_publisher_->publish(map_msg_);
        
        RCLCPP_DEBUG(this->get_logger(), "Published large free map to /global_map");
    }
    
    rclcpp::Publisher<nav_msgs::msg::OccupancyGrid>::SharedPtr map_publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    
    nav_msgs::msg::OccupancyGrid map_msg_;
    int map_width_;
    int map_height_;
    double map_resolution_;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<LargeFreeMapPublisher>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}

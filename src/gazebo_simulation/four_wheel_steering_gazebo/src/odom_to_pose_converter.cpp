#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>

class OdomToPoseConverter : public rclcpp::Node
{
public:
    OdomToPoseConverter() : Node("odom_to_pose_converter")
    {
        // 声明参数 (use_sim_time由ROS2自动声明，不需要手动声明)
        this->declare_parameter<std::string>("odom_topic", "/odom");
        this->declare_parameter<std::string>("pose_topic", "/localization/pose");

        // 获取参数
        std::string odom_topic = this->get_parameter("odom_topic").as_string();
        std::string pose_topic = this->get_parameter("pose_topic").as_string();
        bool use_sim_time = this->get_parameter("use_sim_time").as_bool();

        // 创建订阅者和发布者
        odom_subscriber_ = this->create_subscription<nav_msgs::msg::Odometry>(
            odom_topic, 10,
            std::bind(&OdomToPoseConverter::odom_callback, this, std::placeholders::_1));

        pose_publisher_ = this->create_publisher<geometry_msgs::msg::PoseStamped>(
            pose_topic, 10);

        RCLCPP_INFO(this->get_logger(), "Odom to Pose converter started");
        RCLCPP_INFO(this->get_logger(), "Subscribing to: %s", odom_topic.c_str());
        RCLCPP_INFO(this->get_logger(), "Publishing to: %s", pose_topic.c_str());
        RCLCPP_INFO(this->get_logger(), "Use sim time: %s", use_sim_time ? "true" : "false");
    }

private:
    void odom_callback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        // 转换Odometry消息为PoseStamped消息
        auto pose_msg = std::make_shared<geometry_msgs::msg::PoseStamped>();

        // 复制header信息
        pose_msg->header = msg->header;

        // 复制pose信息
        pose_msg->pose = msg->pose.pose;

        // 发布PoseStamped消息
        pose_publisher_->publish(*pose_msg);

        // 定期打印调试信息
        static auto last_print_time = this->now();
        auto current_time = this->now();
        if ((current_time - last_print_time).seconds() > 2.0) {
            RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                                "Converting odom to pose: [%.3f, %.3f, %.3f] in frame %s",
                                pose_msg->pose.position.x,
                                pose_msg->pose.position.y,
                                pose_msg->pose.position.z,
                                pose_msg->header.frame_id.c_str());
            last_print_time = current_time;
        }
    }

    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_subscriber_;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pose_publisher_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<OdomToPoseConverter>());
    rclcpp::shutdown();
    return 0;
}

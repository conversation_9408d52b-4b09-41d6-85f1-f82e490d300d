#!/bin/bash

# 快速测试3D激光雷达修改的脚本

echo "=== 3D激光雷达修改测试 ==="
echo

# 检查必要的文件是否存在
echo "1. 检查修改的文件..."
files=(
    "urdf/sensors.xacro"
    "urdf/four_wheel_steering_robot.urdf.xacro"
    "launch/four_wheel_steering_simulation.launch.py"
    "package.xml"
)

for file in "${files[@]}"; do
    if [ -f "src/gazebo_simulation/four_wheel_steering_gazebo/$file" ]; then
        echo "   ✓ $file 存在"
    else
        echo "   ✗ $file 不存在"
    fi
done

echo

# 检查URDF是否可以正确解析
echo "2. 检查URDF语法..."
cd /home/<USER>/ir100_ws
source install/setup.bash

# 尝试解析URDF
xacro_result=$(xacro src/gazebo_simulation/four_wheel_steering_gazebo/urdf/four_wheel_steering_robot.urdf.xacro 2>&1)
if [ $? -eq 0 ]; then
    echo "   ✓ URDF语法正确"
else
    echo "   ✗ URDF语法错误:"
    echo "$xacro_result"
fi

echo

# 检查关键修改点
echo "3. 检查关键修改..."

# 检查3D激光雷达配置
if grep -q "vertical" src/gazebo_simulation/four_wheel_steering_gazebo/urdf/sensors.xacro; then
    echo "   ✓ 3D激光雷达垂直扫描配置已添加"
else
    echo "   ✗ 3D激光雷达垂直扫描配置未找到"
fi

if grep -q "PointCloud2" src/gazebo_simulation/four_wheel_steering_gazebo/urdf/sensors.xacro; then
    echo "   ✓ 输出类型已改为PointCloud2"
else
    echo "   ✗ 输出类型未改为PointCloud2"
fi

# 检查位置调整
if grep -q 'z_offset="0.35"' src/gazebo_simulation/four_wheel_steering_gazebo/urdf/four_wheel_steering_robot.urdf.xacro; then
    echo "   ✓ 激光雷达高度已调整"
else
    echo "   ✗ 激光雷达高度未调整"
fi

if grep -q 'x_offset="0.4"' src/gazebo_simulation/four_wheel_steering_gazebo/urdf/four_wheel_steering_robot.urdf.xacro; then
    echo "   ✓ 激光雷达前向位置已调整"
else
    echo "   ✗ 激光雷达前向位置未调整"
fi

# 检查launch文件修改
if grep -q "topic_tools" src/gazebo_simulation/four_wheel_steering_gazebo/launch/four_wheel_steering_simulation.launch.py; then
    echo "   ✓ Launch文件已更新为使用topic_tools"
else
    echo "   ✗ Launch文件未更新"
fi

# 检查依赖
if grep -q "topic_tools" src/gazebo_simulation/four_wheel_steering_gazebo/package.xml; then
    echo "   ✓ topic_tools依赖已添加"
else
    echo "   ✗ topic_tools依赖未添加"
fi

echo

echo "4. 建议的测试步骤:"
echo "   1. 启动仿真: ros2 launch four_wheel_steering_gazebo four_wheel_steering_simulation.launch.py"
echo "   2. 检查话题: ros2 topic list | grep -E '(points|livox)'"
echo "   3. 查看点云: ros2 topic echo /points --once"
echo "   4. 运行测试脚本: python3 src/gazebo_simulation/four_wheel_steering_gazebo/scripts/test_3d_lidar.py"
echo "   5. 在RViz中可视化点云数据"

echo
echo "=== 测试完成 ==="

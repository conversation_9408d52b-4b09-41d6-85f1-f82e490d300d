#!/usr/bin/env python3
"""
测试3D激光雷达的脚本
验证点云数据是否正确输出，并检查射线是否与机身干涉
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np
import time

class LiDAR3DTest(Node):
    def __init__(self):
        super().__init__('lidar_3d_test')
        
        # 订阅3D激光雷达点云数据
        self.pointcloud_sub = self.create_subscription(
            PointCloud2,
            '/points',
            self.pointcloud_callback,
            10
        )
        
        # 订阅重映射后的点云数据
        self.livox_pointcloud_sub = self.create_subscription(
            PointCloud2,
            '/livox/point_cloud',
            self.livox_pointcloud_callback,
            10
        )
        
        self.point_count = 0
        self.last_print_time = time.time()
        
        self.get_logger().info("3D LiDAR测试节点已启动")
        self.get_logger().info("等待点云数据...")

    def pointcloud_callback(self, msg):
        """处理原始点云数据"""
        try:
            # 解析点云数据
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
            
            if points:
                self.point_count += len(points)
                
                # 每5秒打印一次统计信息
                current_time = time.time()
                if current_time - self.last_print_time > 5.0:
                    self.analyze_pointcloud(points, "原始点云")
                    self.last_print_time = current_time
                    
        except Exception as e:
            self.get_logger().error(f"处理原始点云数据时出错: {e}")

    def livox_pointcloud_callback(self, msg):
        """处理重映射后的点云数据"""
        try:
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
            
            if points:
                # 每5秒打印一次统计信息
                current_time = time.time()
                if current_time - self.last_print_time > 5.0:
                    self.analyze_pointcloud(points, "Livox点云")
                    
        except Exception as e:
            self.get_logger().error(f"处理Livox点云数据时出错: {e}")

    def analyze_pointcloud(self, points, source_name):
        """分析点云数据"""
        if not points:
            self.get_logger().warn(f"{source_name}: 没有接收到点云数据")
            return
            
        # 转换为numpy数组进行分析
        points_array = np.array(points)
        
        # 统计信息
        num_points = len(points_array)
        
        # 距离统计
        distances = np.sqrt(np.sum(points_array**2, axis=1))
        min_dist = np.min(distances)
        max_dist = np.max(distances)
        avg_dist = np.mean(distances)
        
        # 高度统计
        z_values = points_array[:, 2]
        min_z = np.min(z_values)
        max_z = np.max(z_values)
        
        # 检查是否有点在机身附近（可能的干涉）
        close_points = points_array[distances < 0.3]  # 距离小于30cm的点
        interference_count = len(close_points)
        
        self.get_logger().info(f"""
{source_name} 统计信息:
  - 点数: {num_points}
  - 距离范围: {min_dist:.2f}m - {max_dist:.2f}m (平均: {avg_dist:.2f}m)
  - 高度范围: {min_z:.2f}m - {max_z:.2f}m
  - 可能干涉点数: {interference_count} (距离<0.3m)
  - 帧率: {msg.header.stamp.sec}.{msg.header.stamp.nanosec}
        """)
        
        if interference_count > 0:
            self.get_logger().warn(f"检测到 {interference_count} 个可能与机身干涉的点!")

def main(args=None):
    rclpy.init(args=args)
    
    test_node = LiDAR3DTest()
    
    try:
        rclpy.spin(test_node)
    except KeyboardInterrupt:
        test_node.get_logger().info("测试节点被用户中断")
    finally:
        test_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

# 3D激光雷达升级说明

## 修改概述

将Gazebo四轮模型中的2D激光雷达升级为3D激光雷达（类似Livox Mid-360），并解决射线与机身干涉问题。

## 主要修改

### 1. 传感器配置 (`urdf/sensors.xacro`)

**原配置 (2D LiDAR):**
- 输出类型: `sensor_msgs/LaserScan`
- 扫描方式: 仅水平360度扫描
- 话题: `/scan`
- 外观: 红色圆柱体 (半径0.05m, 高度0.07m)

**新配置 (3D LiDAR - 优化版):**
- 输出类型: `sensor_msgs/PointCloud2`
- 扫描方式: 水平360度 + 垂直64线扫描
- 垂直角度范围: -25° 到 +15° (类似Mid-360)
- 话题: `/points`
- 外观: 黑色圆柱体 (半径0.04m, 高度0.06m)
- 探测距离: 0.1m - 20m (适合仿真环境)
- 更新频率: 10Hz (平衡性能和精度)
- 水平分辨率: 360个采样点 (1度/点)
- 射线可视化: 关闭 (减少视觉干扰)

### 2. 位置调整 (`urdf/four_wheel_steering_robot.urdf.xacro`)

**原位置:**
- x_offset: 0.3m (机身前方30cm)
- z_offset: 0.15m (机身上方15cm)

**新位置:**
- x_offset: 0.4m (机身前方40cm) - 避免前向射线干涉
- z_offset: 0.35m (机身上方35cm) - 避免下向射线干涉

### 3. 数据处理 (`launch/four_wheel_steering_simulation.launch.py`)

**原方案:**
- 使用 `laserscan_to_pointcloud_node` 将LaserScan转换为PointCloud2
- 输入: `/scan` (LaserScan)
- 输出: `/livox/point_cloud` (PointCloud2)

**新方案:**
- 使用 `topic_tools/relay` 直接重映射PointCloud2
- 输入: `/points` (PointCloud2)
- 输出: `/livox/point_cloud` (PointCloud2)

## 技术参数对比

| 参数 | 2D LiDAR | 3D LiDAR (优化版) |
|------|----------|------------------|
| 扫描线数 | 1线 | 64线 |
| 水平采样点 | 360点 | 360点 |
| 垂直角度 | 0° | -25° ~ +15° |
| 最大距离 | 10m | 20m |
| 最小距离 | 0.1m | 0.1m |
| 更新频率 | 10Hz | 10Hz |
| 输出格式 | LaserScan | PointCloud2 |
| 射线可视化 | 开启 | 关闭 |
| 质量 | 0.5kg | 0.3kg |

## 干涉问题解决

### 问题分析
- 原2D激光雷达位置较低且较近，射线容易击中机身
- 机身尺寸: 1.0m × 0.6m × 0.2m

### 解决方案
1. **提高高度**: z_offset从0.15m增加到0.35m
2. **前移位置**: x_offset从0.3m增加到0.4m
3. **优化垂直角度**: 限制下向扫描角度为-25°

### 验证方法
使用测试脚本检查干涉情况:
```bash
cd /home/<USER>/ir100_ws
python3 src/gazebo_simulation/four_wheel_steering_gazebo/scripts/test_3d_lidar.py
```

## 使用方法

### 启动仿真
```bash
cd /home/<USER>/ir100_ws
ros2 launch four_wheel_steering_gazebo four_wheel_steering_simulation.launch.py
```

### 查看点云数据
```bash
# 原始3D激光雷达数据
ros2 topic echo /points

# 重映射后的数据 (用于mapping_nodes)
ros2 topic echo /livox/point_cloud
```

### 在RViz中可视化
1. 添加PointCloud2显示
2. 话题选择: `/points` 或 `/livox/point_cloud`
3. 固定坐标系: `lidar_link` 或 `base_link`

## 兼容性说明

- **mapping_nodes**: 无需修改，仍然订阅 `/livox/point_cloud`
- **导航系统**: 无需修改，继续使用点云数据
- **可视化**: 需要在RViz中将LaserScan显示改为PointCloud2显示

## 故障排除

### 常见问题
1. **没有点云数据**: 检查Gazebo是否正确加载3D激光雷达插件
2. **数据格式错误**: 确认订阅的是PointCloud2而不是LaserScan
3. **性能问题**: 3D激光雷达数据量大，可能需要调整处理频率

### 调试命令
```bash
# 检查话题列表
ros2 topic list | grep -E "(points|scan|livox)"

# 检查话题信息
ros2 topic info /points
ros2 topic info /livox/point_cloud

# 检查TF树
ros2 run tf2_tools view_frames
```

<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:macro name="robot_base" params="base_width base_length base_height">

    <!-- Base link -->
    <link name="agv_base_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
          <box size="${base_length} ${base_width} ${base_height}"/>
        </geometry>
        <material name="blue">
          <color rgba="0.2 0.2 0.8 1.0"/>
        </material>
      </visual>

      <collision>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
          <box size="${base_length} ${base_width} ${base_height}"/>
        </geometry>
      </collision>

      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="50.0"/>
        <inertia
          ixx="${(1/12) * 50.0 * (base_width*base_width + base_height*base_height)}"
          ixy="0.0"
          ixz="0.0"
          iyy="${(1/12) * 50.0 * (base_length*base_length + base_height*base_height)}"
          iyz="0.0"
          izz="${(1/12) * 50.0 * (base_length*base_length + base_width*base_width)}"/>
      </inertial>
    </link>

    <!-- Chassis link for visual representation -->
    <joint name="chassis_joint" type="fixed">
      <parent link="agv_base_link"/>
      <child link="chassis_link"/>
      <origin xyz="0 0 ${base_height/2 + 0.05}" rpy="0 0 0"/>
    </joint>

    <link name="chassis_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
          <box size="${base_length * 0.8} ${base_width * 0.8} 0.1"/>
        </geometry>
        <material name="gray">
          <color rgba="0.5 0.5 0.5 1.0"/>
        </material>
      </visual>
    </link>

  </xacro:macro>

</robot>

<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="four_wheel_steering_robot">

  <!-- Include macros -->
  <xacro:include filename="$(find four_wheel_steering_gazebo)/urdf/robot_base.xacro"/>
  <xacro:include filename="$(find four_wheel_steering_gazebo)/urdf/wheel.xacro"/>
  <xacro:include filename="$(find four_wheel_steering_gazebo)/urdf/sensors.xacro"/>
  <xacro:include filename="$(find four_wheel_steering_gazebo)/urdf/gazebo_plugins.xacro"/>

  <!-- Robot parameters -->
  <xacro:property name="base_width" value="0.6"/>
  <xacro:property name="base_length" value="1.0"/>
  <xacro:property name="base_height" value="0.2"/>
  <xacro:property name="wheel_radius" value="0.1"/>
  <xacro:property name="wheel_width" value="0.05"/>
  <xacro:property name="wheelbase" value="0.7"/>  <!-- Distance between front and rear axles -->
  <xacro:property name="track_width" value="0.5"/> <!-- Distance between left and right wheels -->

  <!-- Base link (reference frame) -->
  <link name="base_footprint"/>

  <!-- Base link -->
  <joint name="base_joint" type="fixed">
    <parent link="base_footprint"/>
    <child link="agv_base_link"/>
    <origin xyz="0 0 ${wheel_radius}" rpy="0 0 0"/>
  </joint>

  <!-- Robot base -->
  <xacro:robot_base
    base_width="${base_width}"
    base_length="${base_length}"
    base_height="${base_height}"/>

  <!-- Wheels with steering capability -->
  <!-- Front left wheel -->
  <xacro:steered_wheel
    prefix="front_left"
    parent="agv_base_link"
    wheel_radius="${wheel_radius}"
    wheel_width="${wheel_width}"
    x_offset="${wheelbase/2}"
    y_offset="${track_width/2}"
    z_offset="${-wheel_radius}"/>

  <!-- Front right wheel -->
  <xacro:steered_wheel
    prefix="front_right"
    parent="agv_base_link"
    wheel_radius="${wheel_radius}"
    wheel_width="${wheel_width}"
    x_offset="${wheelbase/2}"
    y_offset="${-track_width/2}"
    z_offset="${-wheel_radius}"/>

  <!-- Rear left wheel -->
  <xacro:steered_wheel
    prefix="rear_left"
    parent="agv_base_link"
    wheel_radius="${wheel_radius}"
    wheel_width="${wheel_width}"
    x_offset="${-wheelbase/2}"
    y_offset="${track_width/2}"
    z_offset="${-wheel_radius}"/>

  <!-- Rear right wheel -->
  <xacro:steered_wheel
    prefix="rear_right"
    parent="agv_base_link"
    wheel_radius="${wheel_radius}"
    wheel_width="${wheel_width}"
    x_offset="${-wheelbase/2}"
    y_offset="${-track_width/2}"
    z_offset="${-wheel_radius}"/>

  <!-- Sensors -->
  <!-- 3D激光雷达位置调整：提高高度并向前移动，避免与机身干涉 -->
  <xacro:lidar_sensor parent="agv_base_link" x_offset="0.4" y_offset="0" z_offset="0.35"/>

  <!-- Gazebo plugins -->
  <xacro:four_wheel_steering_plugin
    wheelbase="${wheelbase}"
    track_width="${track_width}"
    wheel_radius="${wheel_radius}"/>

</robot>

<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <xacro:macro name="lidar_sensor" params="parent x_offset y_offset z_offset">

    <!-- 3D LiDAR mount (类似Livox Mid-360) -->
    <joint name="radar_joint" type="fixed">
      <parent link="${parent}"/>
      <child link="radar_link"/>
      <origin xyz="${x_offset} ${y_offset} ${z_offset}" rpy="0 0 0"/>
    </joint>

    <link name="radar_link">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
          <!-- 更小的圆柱体，类似Mid-360的外形 -->
          <cylinder radius="0.04" length="0.06"/>
        </geometry>
        <material name="black">
          <color rgba="0.1 0.1 0.1 1.0"/>
        </material>
      </visual>

      <collision>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <geometry>
          <cylinder radius="0.04" length="0.06"/>
        </geometry>
      </collision>

      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="0.3"/>
        <inertia
          ixx="0.0005" ixy="0.0" ixz="0.0"
          iyy="0.0005" iyz="0.0"
          izz="0.0003"/>
      </inertial>
    </link>

    <!-- Gazebo 3D LiDAR sensor -->
    <gazebo reference="radar_link">
      <material>Gazebo/Black</material>
      <sensor name="radar_3d_sensor" type="ray">
        <pose>0 0 0 0 0 0</pose>
        <visualize>false</visualize>  <!-- 关闭射线可视化，减少视觉干扰 -->
        <update_rate>10</update_rate>  <!-- 降低更新频率，减少计算负载 -->
        <ray>
          <scan>
            <!-- 水平扫描 -->
            <horizontal>
              <samples>1800</samples>
              <resolution>1</resolution>
              <min_angle>-3.14159</min_angle>
              <max_angle>3.14159</max_angle>
            </horizontal>
            <!-- 垂直扫描 - 3D LiDAR的关键特性 -->
            <vertical>
              <samples>64</samples>  <!-- 64线，类似Mid-360的线数 -->
              <resolution>1</resolution>
              <min_angle>-0.436</min_angle>  <!-- -25度 -->
              <max_angle>0.262</max_angle>   <!-- +15度 -->
            </vertical>
          </scan>
          <range>
            <min>0.1</min>
            <max>20.0</max>  <!-- 合理的探测距离 -->
            <resolution>0.01</resolution>
          </range>
          <noise>
            <type>gaussian</type>
            <mean>0.0</mean>
            <stddev>0.02</stddev>
          </noise>
        </ray>
        <plugin name="radar_3d_controller" filename="libgazebo_ros_ray_sensor.so">
          <ros>
            <namespace></namespace>
            <remapping>~/out:=points</remapping>  <!-- 输出点云而不是LaserScan -->
          </ros>
          <output_type>sensor_msgs/PointCloud2</output_type>  <!-- 3D点云输出 -->
          <frame_name>radar_link</frame_name>
        </plugin>
      </sensor>
    </gazebo>

  </xacro:macro>

</robot>

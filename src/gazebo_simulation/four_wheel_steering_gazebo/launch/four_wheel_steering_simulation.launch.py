#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.substitutions import Command, FindExecutable, LaunchConfiguration, PathJoinSubstitution
from launch.event_handlers import OnProcessStart
from launch.actions import ExecuteProcess, RegisterEventHandler, DeclareLaunchArgument
from launch.conditions import IfCondition
from launch_ros.parameter_descriptions import ParameterValue
from launch_ros.actions import Node

def generate_launch_description():
    # Get package directories
    four_wheel_steering_gazebo_dir = get_package_share_directory("four_wheel_steering_gazebo")
    mapping_nodes_dir = get_package_share_directory("mapping_nodes")
    costmap_generator_dir = get_package_share_directory("costmap_generator")
    path_planner_dir = get_package_share_directory("path_planner")
    path_follower_dir = get_package_share_directory("four_wheel_steering_path_follower")

    # File paths
    urdf_path = os.path.join(four_wheel_steering_gazebo_dir, "urdf", "four_wheel_steering_robot.urdf.xacro")
    world_path = os.path.join(four_wheel_steering_gazebo_dir, "worlds", "four_wheel_steering_world.world")
    rviz_config_path = os.path.join(four_wheel_steering_gazebo_dir, "rviz", "four_wheel_steering.rviz")
    control_config_path = os.path.join(four_wheel_steering_gazebo_dir, "config", "four_wheel_steering_control.yaml")
    local_mapping_config_path = os.path.join(mapping_nodes_dir, "config", "local_mapping_params.yaml")

    # Launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation time'
    )

    enable_rviz_arg = DeclareLaunchArgument(
        'enable_rviz',
        default_value='true',
        description='Enable RViz2'
    )

    enable_local_mapping_arg = DeclareLaunchArgument(
        'enable_local_mapping',
        default_value='true',
        description='Enable local mapping'
    )

    enable_navigation_arg = DeclareLaunchArgument(
        'enable_navigation',
        default_value='true',
        description='Enable IR100 navigation stack'
    )

    # Launch configurations
    use_sim_time = LaunchConfiguration('use_sim_time')
    enable_rviz = LaunchConfiguration('enable_rviz')
    enable_local_mapping = LaunchConfiguration('enable_local_mapping')
    enable_navigation = LaunchConfiguration('enable_navigation')

    # Robot description
    robot_description = ParameterValue(
        Command([FindExecutable(name='xacro'), ' ', urdf_path]),
        value_type=str
    )

    # Robot state publisher
    robot_state_publisher_node = Node(
        package="robot_state_publisher",
        executable="robot_state_publisher",
        name="robot_state_publisher",
        output="screen",
        parameters=[
            {
                "use_sim_time": use_sim_time,
                "robot_description": robot_description,
            }
        ],
    )

    # Gazebo server
    gazebo = ExecuteProcess(
        cmd=['gzserver', '--verbose', world_path, '-s', 'libgazebo_ros_factory.so'],
        output='screen'
    )

    # Gazebo client
    gzclient = ExecuteProcess(
        cmd=['gzclient'],
        output='screen'
    )

    # Spawn robot
    spawn_robot = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-topic', 'robot_description',
            '-entity', 'four_wheel_steering_robot',
            '-x', '0.0',
            '-y', '0.0',
            '-z', '0.1'
        ],
        output='screen'
    )

    # Four wheel steering controller (for visual steering angles)
    four_wheel_steering_controller = Node(
        package='four_wheel_steering_gazebo',
        executable='four_wheel_steering_controller_node',
        name='four_wheel_steering_controller',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'wheelbase': 0.7,
                'track_width': 0.5,
                'wheel_radius': 0.1,
                'max_steering_angle': 0.7854,  # 45 degrees
                'base_frame_id': 'agv_base_link',  # Changed to agv_base_link
                'odom_frame_id': 'odom'
            }
        ]
    )

    # 3D LiDAR点云重映射节点 (直接从Gazebo 3D LiDAR获取PointCloud2)
    pointcloud_remapper_node = Node(
        package='topic_tools',
        executable='relay',
        name='pointcloud_remapper',
        output='screen',
        arguments=['/points', '/livox/point_cloud'],
        parameters=[
            {
                'use_sim_time': use_sim_time
            }
        ]
    )

    # Odom到Pose转换节点 (为仿真环境提供/localization/pose话题)
    odom_to_pose_converter_node = Node(
        package='four_wheel_steering_gazebo',
        executable='odom_to_pose_converter_node',
        name='odom_to_pose_converter',
        output='screen',
        parameters=[
            {
                'use_sim_time': use_sim_time,
                'odom_topic': '/odom',
                'pose_topic': '/localization/pose'
            }
        ]
    )

    # Local mapping node
    local_mapping_node = Node(
        package='mapping_nodes',
        executable='local_mapping_node',
        name='local_mapping',
        output='screen',
        parameters=[
            local_mapping_config_path,  # Load from config file
            {
                'use_sim_time': use_sim_time,
                # Override specific parameters for simulation
                'map_size': 10.0,  # 10x10 meters for simulation
                'odometry_topic': '/odom',  # Use simulation odometry topic
                'map_frame_id': 'radar_link',  # Use radar_link to match pointcloud frame
                'base_frame_id': 'radar_link',  # Use radar_link as base frame
                'lidar_frame_id': 'radar_link',  # LiDAR frame matches map frame
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_local_mapping'))
    )

    # IR100 Navigation Components
    # Costmap generator
    costmap_generator_node = Node(
        package='costmap_generator',
        executable='costmap_generator_node',
        name='costmap_generator',
        output='screen',
        parameters=[
            PathJoinSubstitution([costmap_generator_dir, 'config', 'costmap_params.yaml']),
            {
                'use_sim_time': use_sim_time,
                'map_topic': 'local_map',
                'costmap_topic': 'local_costmap'
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_navigation'))
    )

    # Path planner
    path_planner_node = Node(
        package='path_planner',
        executable='path_planner_node',
        name='path_planner',
        output='screen',
        parameters=[
            PathJoinSubstitution([path_planner_dir, 'config', 'path_planner_config.yaml']),
            {
                'use_sim_time': use_sim_time,
                'costmap_topic': 'local_costmap',  # Use global map for planning
                'path_topic': 'planned_path'
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_navigation'))
    )

    # Path smoother
    path_smoother_dir = get_package_share_directory('path_smoother')
    path_smoother_node = Node(
        package='path_smoother',
        executable='path_smoother_node',
        name='path_smoother',
        output='screen',
        parameters=[
            PathJoinSubstitution([path_smoother_dir, 'config', 'smoother_params.yaml']),
            {'use_sim_time': use_sim_time}
        ],
        condition=IfCondition(LaunchConfiguration('enable_navigation'))
    )

    # Path follower
    path_follower_node = Node(
        package='four_wheel_steering_path_follower',
        executable='four_wheel_steering_path_follower_node',
        name='four_wheel_steering_path_follower_node',
        output='screen',
        parameters=[
            PathJoinSubstitution([path_follower_dir, 'config', 'four_wheel_steering_params.yaml']),
            {
                'use_sim_time': use_sim_time,
                'path_topic': 'smoothed_local_path',  # Use smoothed path from smoother
                'cmd_vel_topic': 'cmd_vel',
                'odom_topic': 'odom'
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_navigation'))
    )

    # Large free map publisher for global map
    large_free_map_publisher_node = Node(
        package='four_wheel_steering_gazebo',
        executable='large_free_map_publisher_node',
        name='large_free_map_publisher',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time
        }]
    )

    # Keyboard teleop for manual control
    keyboard_teleop_node = Node(
        package='teleop_twist_keyboard',
        executable='teleop_twist_keyboard',
        name='keyboard_teleop',
        output='screen',
        parameters=[{'use_sim_time': use_sim_time}]
    )

    # Static transform publisher for map -> odom (for simulation)
    map_to_odom_tf = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_to_odom_publisher',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom'],
        parameters=[{'use_sim_time': use_sim_time}]
    )

    # RViz2
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_path],
        output='screen',
        parameters=[{'use_sim_time': use_sim_time}],
        condition=IfCondition(LaunchConfiguration('enable_rviz'))
    )

    # Event handler to spawn robot after Gazebo starts
    spawn_event = RegisterEventHandler(
        event_handler=OnProcessStart(
            target_action=gazebo,
            on_start=[spawn_robot]
        )
    )

    return LaunchDescription([
        # Launch arguments
        use_sim_time_arg,
        enable_rviz_arg,
        enable_local_mapping_arg,
        enable_navigation_arg,

        # Core simulation nodes
        gazebo,
        gzclient,
        robot_state_publisher_node,
        spawn_event,

        # Robot control
        four_wheel_steering_controller,

        # Sensor processing
        pointcloud_remapper_node,
        odom_to_pose_converter_node,

        # Mapping
        local_mapping_node,

        # Navigation
        large_free_map_publisher_node,
        costmap_generator_node,
        path_planner_node,
        path_smoother_node,
        path_follower_node,
        map_to_odom_tf,

        # Manual control
        keyboard_teleop_node,

        # Visualization
        rviz_node,
    ])

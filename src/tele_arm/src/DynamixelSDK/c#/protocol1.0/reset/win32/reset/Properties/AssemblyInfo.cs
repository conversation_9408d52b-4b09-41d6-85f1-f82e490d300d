﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;




[assembly: AssemblyTitle("reset")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("reset")]
[assembly: AssemblyCopyright("Copyright ©  2016")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

 
 

[assembly: ComVisible(false)]


[assembly: Guid("9e063190-2f38-4ec6-92b8-9106b4a076da")]


//

 


//
 

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]

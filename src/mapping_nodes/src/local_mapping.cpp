#include "mapping_nodes/local_mapping.hpp"
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <pcl_conversions/pcl_conversions.h>
#include <cmath>
#include <chrono>
#include <thread>
#include <algorithm>

namespace mapping_nodes
{

LocalMapping::LocalMapping(const rclcpp::NodeOptions & options)
: Node("local_mapping", options),
  global_map_received_(false),
  pose_received_(false),
  pointcloud_received_(false),
  new_pointcloud_available_(false),
  should_stop_update_thread_(false)
{
    declare_parameters();

    // Get parameters
    this->get_parameter("base_frame_id", base_frame_id_);
    this->get_parameter("map_frame_id", map_frame_id_);
    this->get_parameter("lidar_frame_id", lidar_frame_id_);
    this->get_parameter("pointcloud_topic", pointcloud_topic_);
    this->get_parameter("odometry_topic", odometry_topic_);
    this->get_parameter("global_map_topic", global_map_topic_);
    this->get_parameter("pose_topic", pose_topic_);
    this->get_parameter("max_time_diff", max_time_diff_);
    this->get_parameter("map_size", map_size_);

    // Get 2.5D mapping parameters
    this->get_parameter("use_layered_mapping", use_layered_mapping_);
    this->get_parameter("layer_height", layer_height_);
    this->get_parameter("robot_traversable_min", robot_traversable_min_);
    this->get_parameter("robot_traversable_max", robot_traversable_max_);
    this->get_parameter("robot_radius", robot_radius_);

    bool use_persistent_layers = false;
    this->get_parameter("use_persistent_layers", use_persistent_layers);

    // Get robot body rectangular filtering parameters
    this->get_parameter("robot_body_min_x", robot_body_min_x_);
    this->get_parameter("robot_body_max_x", robot_body_max_x_);
    this->get_parameter("robot_body_min_y", robot_body_min_y_);
    this->get_parameter("robot_body_max_y", robot_body_max_y_);
    this->get_parameter("map_resolution", map_resolution_);
    this->get_parameter("height_min", height_min_);
    this->get_parameter("height_max", height_max_);
    this->get_parameter("point_skip_filter", point_skip_filter_);
    // this->get_parameter("voxel_size", voxel_size_); // REMOVED
    this->get_parameter("publish_rate", publish_rate_);
    this->get_parameter("map_update_rate", map_update_rate_);

    // Get probabilistic mapping parameters
    int hit_inc, miss_inc, free_thresh, occ_thresh, min_odds, max_odds;
    this->get_parameter("hit_increment", hit_inc);
    this->get_parameter("miss_increment", miss_inc);
    this->get_parameter("free_threshold", free_thresh);
    this->get_parameter("occupied_threshold", occ_thresh);
    this->get_parameter("min_log_odds", min_odds);
    this->get_parameter("max_log_odds", max_odds);
    
    // Convert to int8_t with bounds checking
    hit_increment_ = static_cast<int8_t>(std::max(-127, std::min(127, hit_inc)));
    miss_increment_ = static_cast<int8_t>(std::max(-127, std::min(127, miss_inc)));
    free_threshold_ = static_cast<int8_t>(std::max(-127, std::min(127, free_thresh)));
    occupied_threshold_ = static_cast<int8_t>(std::max(-127, std::min(127, occ_thresh)));
    min_log_odds_ = static_cast<int8_t>(std::max(-127, std::min(127, min_odds)));
    max_log_odds_ = static_cast<int8_t>(std::max(-127, std::min(127, max_odds)));

    // Get motion compensation parameters
    this->get_parameter("enable_motion_compensation", enable_motion_compensation_);
    this->get_parameter("max_angular_velocity", max_angular_velocity_);

    // Get global map fusion parameters
    this->get_parameter("enable_global_fusion", enable_global_fusion_);
    this->get_parameter("global_fusion_conservative", global_fusion_conservative_);
    this->get_parameter("global_fusion_marker_value", global_fusion_marker_value_);



    // Initialize state variables
    current_angular_velocity_ = 0.0;

    // Initialize TF
    tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
    tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);

    // Lookup static TF (radar_link -> base_link) with retry
    lidar_to_base_ready_ = false;
    int tf_retry_count = 0;
    const int max_tf_retries = 10;
    while (!lidar_to_base_ready_ && tf_retry_count < max_tf_retries) {
        try {
            // Wait a bit for TF to be available
            if (tf_retry_count > 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }

            lidar_to_base_tf_ = tf_buffer_->lookupTransform(
                base_frame_id_,     // target
                lidar_frame_id_,    // source
                tf2::TimePointZero);
            lidar_to_base_ready_ = true;
            RCLCPP_INFO(this->get_logger(), "Static TF %s->%s acquired on attempt %d",
                       lidar_frame_id_.c_str(), base_frame_id_.c_str(), tf_retry_count + 1);
        } catch (const tf2::TransformException &ex) {
            tf_retry_count++;
            RCLCPP_WARN(this->get_logger(), "TF attempt %d failed: %s->%s: %s",
                       tf_retry_count, lidar_frame_id_.c_str(), base_frame_id_.c_str(), ex.what());
        }
    }

    if (!lidar_to_base_ready_) {
        RCLCPP_ERROR(this->get_logger(), "Failed to acquire static TF %s->%s after %d attempts",
                    lidar_frame_id_.c_str(), base_frame_id_.c_str(), max_tf_retries);
    }

    // initialize map center in map frame (assume start at (0,0))
    map_center_x_ = 0.0;
    map_center_y_ = 0.0;

    // Initialize point cloud buffers
    latest_pointcloud_.reset(new pcl::PointCloud<pcl::PointXYZ>);
    latest_transformed_cloud_.reset(new pcl::PointCloud<pcl::PointXYZ>);

    // Initialize map and filters
    initialize_map();
    initialize_filters();

    // Initialize appropriate mapping mode
    if (use_layered_mapping_) {
        initialize_layered_mapping();
        // 设置持久化选项
        if (layered_mapper_) {
            layered_mapper_->enable_persistence(use_persistent_layers);
            if (use_persistent_layers) {
                RCLCPP_INFO(this->get_logger(), "Using 2.5D layered mapping mode with persistent submap storage");
            } else {
                RCLCPP_INFO(this->get_logger(), "Using 2.5D layered mapping mode (single-frame observation)");
            }
        }
    } else {
        initialize_cell_states();
        RCLCPP_INFO(this->get_logger(), "Using 2D direct state mapping mode");
    }

    // Pre-compute robot radius area indices for performance optimization
    precompute_robot_radius_indices();
    RCLCPP_INFO(this->get_logger(), "Pre-computed %zu indices within robot radius %.2fm",
                robot_radius_indices_.size(), robot_radius_);

    // Create publishers and subscribers
    local_map_publisher_ = this->create_publisher<nav_msgs::msg::OccupancyGrid>(
        "local_map", rclcpp::QoS(1).transient_local());

    // Publisher for debugging: transformed point cloud in map frame
    transformed_cloud_publisher_ = this->create_publisher<sensor_msgs::msg::PointCloud2>(
        "transformed_cloud", rclcpp::SensorDataQoS());

    pointcloud_subscriber_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
        pointcloud_topic_, rclcpp::SensorDataQoS(),
        std::bind(&LocalMapping::pointcloud_callback, this, std::placeholders::_1));

    odometry_subscriber_ = this->create_subscription<nav_msgs::msg::Odometry>(
        odometry_topic_, 10,
        std::bind(&LocalMapping::odometry_callback, this, std::placeholders::_1));

    global_map_subscriber_ = this->create_subscription<nav_msgs::msg::OccupancyGrid>(
        global_map_topic_, rclcpp::QoS(1).transient_local(),
        std::bind(&LocalMapping::global_map_callback, this, std::placeholders::_1));

    // Create pose subscriber - this is now the only source of localization
    pose_subscriber_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
        pose_topic_, 10,
        std::bind(&LocalMapping::pose_callback, this, std::placeholders::_1));

    // Create timer for publishing
    auto timer_period = std::chrono::milliseconds(static_cast<int>(1000.0 / publish_rate_));
    publish_timer_ = this->create_wall_timer(
        timer_period, std::bind(&LocalMapping::timer_callback, this));

    // Start map update thread
    map_update_thread_ = std::thread(&LocalMapping::map_update_thread_function, this);

    RCLCPP_INFO(this->get_logger(), "[INIT] Local mapping node initialized successfully");
    RCLCPP_INFO(this->get_logger(), "[INIT] Map size: %.1f m, Resolution: %.3f m/pixel", map_size_, map_resolution_);
    RCLCPP_INFO(this->get_logger(), "[INIT] Map update rate: %.1f Hz, Publish rate: %.1f Hz", map_update_rate_, publish_rate_);
    RCLCPP_INFO(this->get_logger(), "[INIT] Using %s mapping mode", use_layered_mapping_ ? "2.5D layered" : "2D direct");
    RCLCPP_INFO(this->get_logger(), "[INIT] Subscribed to: pointcloud=%s, pose=%s, odometry=%s", 
                pointcloud_topic_.c_str(), pose_topic_.c_str(), odometry_topic_.c_str());
    RCLCPP_INFO(this->get_logger(), "[INIT] Publishing local_map and transformed_cloud topics");
}

LocalMapping::~LocalMapping()
{
    // Stop the update thread
    should_stop_update_thread_ = true;
    if (map_update_thread_.joinable()) {
        map_update_thread_.join();
    }
}

void LocalMapping::declare_parameters()
{
    this->declare_parameter<std::string>("base_frame_id", "agv_base_link");
    this->declare_parameter<std::string>("map_frame_id", "map");
    this->declare_parameter<std::string>("lidar_frame_id", "radar_link");
    this->declare_parameter<std::string>("pointcloud_topic", "/livox/point_cloud");
    this->declare_parameter<std::string>("odometry_topic", "/Odometry");
    this->declare_parameter<std::string>("global_map_topic", "/global_map");
    this->declare_parameter<std::string>("pose_topic", "/localization/pose");
    this->declare_parameter<double>("max_time_diff", 0.5);  // Maximum time difference in seconds
    this->declare_parameter<double>("map_size", 5.0);
    this->declare_parameter<double>("map_resolution", 0.05);
    this->declare_parameter<double>("height_min", -0.5);
    this->declare_parameter<double>("height_max", 2.0);
    this->declare_parameter<int>("point_skip_filter", 1);
    // this->declare_parameter<double>("voxel_size", 0.02); // REMOVED
    this->declare_parameter<double>("publish_rate", 10.0);
    this->declare_parameter<double>("map_update_rate", 10.0);

    // No probabilistic parameters needed for direct state mapping

    // Motion compensation parameters
    this->declare_parameter<bool>("enable_motion_compensation", true);
    this->declare_parameter<double>("max_angular_velocity", 1.0); // rad/s threshold for motion compensation

    // 2.5D layered mapping parameters
    this->declare_parameter<bool>("use_layered_mapping", false);
    this->declare_parameter<double>("layer_height", 0.1);  // Height of each layer in meters
    this->declare_parameter<double>("robot_traversable_min", -0.5);  // Robot traversability lower bound
    this->declare_parameter<double>("robot_traversable_max", 0.5);   // Robot traversability upper bound
    this->declare_parameter<double>("robot_radius", 0.5);            // Robot radius for self-filtering
    this->declare_parameter<bool>("use_persistent_layers", false);   // Enable persistent layer storage with submaps

    // Robot body rectangular filtering parameters (relative to radar_link)
    this->declare_parameter<double>("robot_body_min_x", -1.0);       // Robot body minimum x (behind radar)
    this->declare_parameter<double>("robot_body_max_x", 1.0);        // Robot body maximum x (in front of radar)
    this->declare_parameter<double>("robot_body_min_y", -0.6);       // Robot body minimum y (left side)
    this->declare_parameter<double>("robot_body_max_y", 0.6);        // Robot body maximum y (right side)

    // Probabilistic mapping parameters (int8_t for memory efficiency)
    this->declare_parameter<int>("hit_increment", 15);
    this->declare_parameter<int>("miss_increment", -10);
    this->declare_parameter<int>("free_threshold", -80);
    this->declare_parameter<int>("occupied_threshold", 120);
    this->declare_parameter<int>("min_log_odds", -127);
    this->declare_parameter<int>("max_log_odds", 127);

    // Global map fusion parameters
    this->declare_parameter<bool>("enable_global_fusion", true);
    this->declare_parameter<bool>("global_fusion_conservative", true);
    this->declare_parameter<int>("global_fusion_marker_value", 70);
}

void LocalMapping::initialize_map()
{
    // Calculate map dimensions
    map_width_ = static_cast<int>(map_size_ / map_resolution_);
    map_height_ = static_cast<int>(map_size_ / map_resolution_);

    // Pre-allocate map data
    map_data_.resize(map_width_ * map_height_, -1);  // Initialize as unknown

    // Setup local map message - use map frame_id (not base_link frame anymore)
    local_map_.header.frame_id = map_frame_id_;
    local_map_.info.resolution = map_resolution_;
    local_map_.info.width = map_width_;
    local_map_.info.height = map_height_;

    // Initialize origin - will be updated dynamically based on robot pose in update_map_origin()
    // The map will be centered on the current agv_base_link position in map frame
    local_map_.info.origin.position.x = 0.0;
    local_map_.info.origin.position.y = 0.0;
    local_map_.info.origin.position.z = 0.0;
    local_map_.info.origin.orientation.w = 1.0;

    local_map_.data.resize(map_width_ * map_height_);

    RCLCPP_INFO(this->get_logger(), "Initialized local map: %dx%d pixels in %s frame", map_width_, map_height_, map_frame_id_.c_str());
}

void LocalMapping::initialize_cell_states()
{
    int total_cells = map_width_ * map_height_;

    // Initialize cell state arrays
    cell_states_.resize(total_cells, CellState::UNKNOWN);
    cell_observed_this_scan_.resize(total_cells, false);

    RCLCPP_INFO(this->get_logger(), "Initialized direct state mapping");
}

void LocalMapping::initialize_filters()
{
    // Setup voxel grid filter for downsampling
    // voxel_filter_.setLeafSize(voxel_size_, voxel_size_, voxel_size_); // REMOVED

    // Setup height filter
    height_filter_.setFilterFieldName("z");
    height_filter_.setFilterLimits(height_min_, height_max_);
}

void LocalMapping::pointcloud_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
{
    try {
        auto lidar_timestamp = rclcpp::Time(msg->header.stamp).seconds();
        auto current_time = this->now().seconds();
        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                            "[POINTCLOUD_CALLBACK] Received point cloud with %d points, lidar_timestamp: %.6f, current_time: %.6f, age: %.3f sec", 
                            msg->width * msg->height, lidar_timestamp, current_time, current_time - lidar_timestamp);

        // Convert ROS message to PCL
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::fromROSMsg(*msg, *cloud);

        if (cloud->empty()) {
            RCLCPP_WARN(this->get_logger(), "Received empty point cloud");
            return;
        }

        // Store the latest point cloud data for processing by update thread
        {
            std::lock_guard<std::mutex> lock(pointcloud_mutex_);
            latest_pointcloud_ = cloud;
            latest_pointcloud_timestamp_ = msg->header.stamp;
            new_pointcloud_available_ = true;
            pointcloud_received_ = true;

            // Clean up old pointclouds before adding new one
            double current_time_sec = stamp_to_double(msg->header.stamp);
            while (!cloud_queue_.empty()) {
                double oldest_time_sec = stamp_to_double(cloud_queue_.front()->header.stamp);
                if (current_time_sec - oldest_time_sec > max_time_diff_) {
                    RCLCPP_DEBUG(this->get_logger(), "[POINTCLOUD_CALLBACK] Removing old cloud: %.6f (age: %.3fs)", 
                                oldest_time_sec, current_time_sec - oldest_time_sec);
                    cloud_queue_.pop_front();
                } else {
                    break;
                }
            }
            
            // Push raw ROS message to queue for timestamp alignment
            cloud_queue_.push_back(msg);
            
            // Hard limit on queue size
            if (cloud_queue_.size() > 50) {
                RCLCPP_WARN(this->get_logger(), "[POINTCLOUD_CALLBACK] Queue size limit reached, removing oldest cloud");
                cloud_queue_.pop_front();
            }
            
            RCLCPP_DEBUG(this->get_logger(), "[POINTCLOUD_CALLBACK] Queue size: %zu, latest timestamp: %.6f", 
                        cloud_queue_.size(), rclcpp::Time(msg->header.stamp).seconds());
        }

        RCLCPP_DEBUG(this->get_logger(), "Stored point cloud with %zu points for processing", cloud->size());

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Error storing point cloud: %s", e.what());
    }
}

void LocalMapping::odometry_callback(const nav_msgs::msg::Odometry::SharedPtr msg)
{
    std::lock_guard<std::mutex> lock(pose_mutex_);
    // Only use odometry for angular velocity information for motion compensation
    current_angular_velocity_ = std::abs(msg->twist.twist.angular.z);
}

void LocalMapping::global_map_callback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
{
    std::lock_guard<std::mutex> lock(map_mutex_);
    global_map_ = *msg;
    global_map_received_ = true;
    static bool map_received_printed = false;
    if (global_map_received_ && !map_received_printed) {
        map_received_printed = true;
        RCLCPP_INFO(this->get_logger(), "Received global map: %dx%d",
                    global_map_.info.width, global_map_.info.height);
    }
}

void LocalMapping::pose_callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
{
    std::lock_guard<std::mutex> lock(pose_mutex_);
    current_pose_stamped_ = *msg;
    pose_received_ = true;

    // push to queue
    pose_queue_.push_back({rclcpp::Time(msg->header.stamp), msg->pose});
    // keep last 100 poses (~10s if 10Hz)
    if (pose_queue_.size() > 100) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                           "[POSE_CALLBACK] Pose queue size limit reached, removing oldest pose");
        pose_queue_.pop_front();
    }

    RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                        "[POSE_CALLBACK] Received pose at time: %.6f, [%.3f, %.3f, %.3f] frame: %s, queue size: %zu",
                        stamp_to_double(msg->header.stamp), msg->pose.position.x, msg->pose.position.y, msg->pose.position.z,
                        msg->header.frame_id.c_str(), pose_queue_.size());
}


pcl::PointCloud<pcl::PointXYZ>::Ptr LocalMapping::filter_pointcloud(
    const pcl::PointCloud<pcl::PointXYZ>::Ptr& input_cloud)
{
    pcl::PointCloud<pcl::PointXYZ>::Ptr radius_filtered(new pcl::PointCloud<pcl::PointXYZ>);
    pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>);

    // 1. Apply robot body rectangular filter (机器人长方形过滤 - 移除机器人自身区域的点)
    // 注意：这个过滤在radar_link坐标系下进行，因为机器人体参数是相对于radar_link定义的
    radius_filtered->reserve(input_cloud->size());
    for (const auto& point : input_cloud->points) {
        // 检查点是否在机器人长方形区域内
        if (point.x >= robot_body_min_x_ && point.x <= robot_body_max_x_ &&
            point.y >= robot_body_min_y_ && point.y <= robot_body_max_y_) {
            // 点在机器人区域内，跳过
            continue;
        }
        radius_filtered->points.push_back(point);
    }
    radius_filtered->width = radius_filtered->points.size();
    radius_filtered->height = 1;
    radius_filtered->is_dense = true;

    // 2. Apply point skip filter (skip every Nth point for performance)
    filtered_cloud->reserve(radius_filtered->size() / point_skip_filter_);
    for (size_t i = 0; i < radius_filtered->points.size(); i += point_skip_filter_) {
        filtered_cloud->points.push_back(radius_filtered->points[i]);
    }
    filtered_cloud->width = filtered_cloud->points.size();
    filtered_cloud->height = 1;
    filtered_cloud->is_dense = true;

    return filtered_cloud;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr LocalMapping::filter_pointcloud_height_in_map(
    const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud_map)
{
    pcl::PointCloud<pcl::PointXYZ>::Ptr height_filtered(new pcl::PointCloud<pcl::PointXYZ>);
    
    // 在map坐标系下进行高度过滤 - 假设Z=0为地面
    height_filtered->reserve(cloud_map->size());
    for (const auto& point : cloud_map->points) {
        // 检查点的Z坐标是否在允许范围内
        if (point.z >= height_min_ && point.z <= height_max_) {
            height_filtered->points.push_back(point);
        }
    }
    height_filtered->width = height_filtered->points.size();
    height_filtered->height = 1;
    height_filtered->is_dense = true;

    return height_filtered;
}

void LocalMapping::integrate_pointcloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud)
{
    RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                          "=== PROCESSING POINT CLOUD (MAP FRAME) ===");
    RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                          "Point cloud size: %zu points (already in map frame)", cloud->size());

    // Get sensor origin in map frame
    pcl::PointXYZ sensor_origin = get_sensor_origin_in_map_frame();

    // Debug: Print sensor origin
    RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                          "Sensor origin in %s frame: [%.6f, %.6f, %.6f]",
                          map_frame_id_.c_str(), sensor_origin.x, sensor_origin.y, sensor_origin.z);

    int valid_points = 0;
    int processed_points = 0;

    // Process each point in the cloud (already in map frame)
    static int debug_call_count = 0;
    static int total_iterations = 0;
    static int skipped_points = 0;

    RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                          "🔍 INTEGRATION_DEBUG: Starting point loop with %zu points", cloud->points.size());

    for (const auto& point : cloud->points) {
        total_iterations++;

        // Debug first few iterations (only for debugging, throttled)
        if (total_iterations <= 5) {
            RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                       "🔍 Point[%d]: [%.3f, %.3f, %.3f], finite: [%s, %s, %s]",
                       total_iterations-1, point.x, point.y, point.z,
                       std::isfinite(point.x) ? "Y" : "N",
                       std::isfinite(point.y) ? "Y" : "N",
                       std::isfinite(point.z) ? "Y" : "N");
        }

        // Skip invalid points
        if (!std::isfinite(point.x) || !std::isfinite(point.y) || !std::isfinite(point.z)) {
            skipped_points++;
            if (skipped_points <= 3) {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                           "⚠️ Skipping invalid point[%d]: [%.3f, %.3f, %.3f]",
                           total_iterations-1, point.x, point.y, point.z);
            }
            continue;
        }
        valid_points++;

        // Point is already in map frame, no transformation needed
        pcl::PointXYZ point_map(point.x, point.y, point.z);

        // Debug: Log some ray tracing calls
        if (debug_call_count < 3) {
            RCLCPP_ERROR(this->get_logger(), "🔫 FORCE_CALL[%d]: BEFORE ray_trace_and_update(sensor:[%.3f,%.3f], point:[%.3f,%.3f])",
                        debug_call_count, sensor_origin.x, sensor_origin.y, point_map.x, point_map.y);
        }

        // Perform ray tracing from sensor to point (both in map frame)
        ray_trace_and_update(sensor_origin, point_map);

        if (debug_call_count < 3) {
            RCLCPP_ERROR(this->get_logger(), "🔫 FORCE_CALL[%d]: AFTER ray_trace_and_update - returned successfully",
                        debug_call_count);
            debug_call_count++;
        }
        processed_points++;
    }

    RCLCPP_INFO(this->get_logger(), "🔍 INTEGRATION_DEBUG: Processed %d iterations, %d valid, %d skipped, %d ray-traced",
               total_iterations, valid_points, skipped_points, processed_points);

    RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                        "Integrated pointcloud: %d valid points out of %zu total, %d processed (map frame)",
                        valid_points, cloud->size(), processed_points);
}

bool LocalMapping::world_to_map(double world_x, double world_y, int& map_x, int& map_y) const
{
    // Convert world coordinates (in map frame) to map pixel coordinates
    map_x = static_cast<int>((world_x - local_map_.info.origin.position.x) / map_resolution_);
    map_y = static_cast<int>((world_y - local_map_.info.origin.position.y) / map_resolution_);

    bool in_bounds = (map_x >= 0 && map_x < map_width_ && map_y >= 0 && map_y < map_height_);

    static int debug_count = 0;
    if (debug_count < 5) {
        RCLCPP_INFO(rclcpp::get_logger("world_to_map_debug"),
                    "🔄 COORD TRANSFORM: World(%.3f, %.3f) -> Map(%d, %d), Origin(%.3f, %.3f), InBounds=%s",
                    world_x, world_y, map_x, map_y,
                    local_map_.info.origin.position.x, local_map_.info.origin.position.y,
                    in_bounds ? "YES" : "NO");
        debug_count++;
    }

    return in_bounds;
}

void LocalMapping::map_to_world(int map_x, int map_y, double& world_x, double& world_y) const
{
    // Convert map pixel coordinates to world coordinates (in map frame)
    world_x = local_map_.info.origin.position.x + (map_x + 0.5) * map_resolution_;
    world_y = local_map_.info.origin.position.y + (map_y + 0.5) * map_resolution_;
}

int LocalMapping::get_map_index(int x, int y) const
{
    return y * map_width_ + x;
}



void LocalMapping::timer_callback()
{
    // Check if we have essential data before publishing
    if (!pointcloud_received_) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                           "[TIMER_CALLBACK] No radar/lidar data received yet - not publishing map. "
                           "Waiting for data on topic: %s", pointcloud_topic_.c_str());
        return;  // Don't publish map without radar data
    }

    if (!pose_received_) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                           "[TIMER_CALLBACK] No pose data received yet - not publishing map. "
                           "Waiting for data on topic: %s", pose_topic_.c_str());
        return;  // Don't publish map without pose data
    }

    // Update map header
    local_map_.header.stamp = this->now();
    local_map_.header.frame_id = map_frame_id_;

    // Log successful publishing with current map statistics
    int occupied_count = 0, free_count = 0, unknown_count = 0;
    for (const auto& value : local_map_.data) {
        if (value == 100) occupied_count++;
        else if (value == 0) free_count++;
        else unknown_count++;
    }

    local_map_publisher_->publish(local_map_);
    
    RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                        "[TIMER_CALLBACK] Published map: %dx%d, resolution: %.3f, occupied: %d, free: %d, unknown: %d",
                        local_map_.info.width, local_map_.info.height, local_map_.info.resolution,
                        occupied_count, free_count, unknown_count);
}

void LocalMapping::update_cell_state(int index, bool is_occupied)
{
    static int update_count = 0;
    static int debug_updates = 0;
    update_count++;

    bool should_debug_update = debug_updates < 10;

    if (index < 0 || index >= static_cast<int>(cell_states_.size())) {
        if (should_debug_update) {
            RCLCPP_ERROR(this->get_logger(), "🚫 CELL_UPDATE_ERROR: Index %d out of bounds [0, %zu)",
                        index, cell_states_.size());
            debug_updates++;
        }
        return;
    }

    CellState old_state = cell_states_[index];

    // Check if this cell was already observed in current scan
    if (cell_observed_this_scan_[index]) {
        // Cell already observed in this scan, apply conflict resolution logic
        CellState current_state = cell_states_[index];
        CellState new_observation = is_occupied ? CellState::OCCUPIED : CellState::FREE;

        // If both occupy and free observed in same scan, occupy wins
        if ((current_state == CellState::OCCUPIED && new_observation == CellState::FREE) ||
            (current_state == CellState::FREE && new_observation == CellState::OCCUPIED)) {
            cell_states_[index] = CellState::OCCUPIED;

            if (should_debug_update) {
                RCLCPP_INFO(this->get_logger(), "🔄 CELL_CONFLICT[%d]: Index=%d, %s + %s -> OCCUPIED",
                           debug_updates, index,
                           current_state == CellState::OCCUPIED ? "OCC" : "FREE",
                           new_observation == CellState::OCCUPIED ? "OCC" : "FREE");
                debug_updates++;
            }
        }
        // If both are the same, keep current state (no change needed)
    } else {
        // First observation of this cell in current scan
        cell_observed_this_scan_[index] = true;

        // Only update if we have a new observation
        if (is_occupied) {
            cell_states_[index] = CellState::OCCUPIED;
        } else {
            cell_states_[index] = CellState::FREE;
        }

        if (should_debug_update) {
            RCLCPP_INFO(this->get_logger(), "📝 CELL_UPDATE[%d]: Index=%d, %s -> %s",
                       debug_updates, index,
                       old_state == CellState::UNKNOWN ? "UNK" : (old_state == CellState::OCCUPIED ? "OCC" : "FREE"),
                       is_occupied ? "OCC" : "FREE");
            debug_updates++;
        }
    }
}

void LocalMapping::ray_trace_and_update(const pcl::PointXYZ& sensor_origin, const pcl::PointXYZ& point)
{
    // Convert sensor origin to map coordinates
    int sensor_x, sensor_y;
    if (!world_to_map(sensor_origin.x, sensor_origin.y, sensor_x, sensor_y)) {
        static int failure_count = 0;
        if (failure_count < 3) {
            RCLCPP_WARN(this->get_logger(), "Ray trace sensor origin [%.3f, %.3f] outside map bounds - skipping ray",
                        sensor_origin.x, sensor_origin.y);
            failure_count++;
        }
        return;
    }

    // Debug for first few rays
    static int debug_count = 0;
    bool should_debug = (debug_count < 5);
    if (should_debug) {
        debug_count++;
        RCLCPP_INFO(this->get_logger(), "✅ RAY_TRACE_DEBUG: Sensor origin [%.3f, %.3f] -> Map[%d, %d] - ray tracing starting",
                    sensor_origin.x, sensor_origin.y, sensor_x, sensor_y);
    }

    // Calculate target point in map coordinates (may be outside map bounds)
    int target_x, target_y;
    bool target_in_map = world_to_map(point.x, point.y, target_x, target_y);

    if (should_debug) {
        RCLCPP_INFO(this->get_logger(), "🎯 TARGET_DEBUG: Point[%.3f, %.3f] -> Map[%d, %d], InMap=%s",
                   point.x, point.y, target_x, target_y, target_in_map ? "YES" : "NO");
    }

    // If target is outside map, calculate intersection with map boundary
    int end_x, end_y;
    bool has_obstacle = false;

    if (target_in_map) {
        // Target is within map bounds
        end_x = target_x;
        end_y = target_y;
        has_obstacle = true;  // Mark endpoint as occupied

        if (should_debug) {
            RCLCPP_INFO(this->get_logger(), "✅ ENDPOINT: Target in map, end[%d,%d], has_obstacle=true",
                       end_x, end_y);
        }
    } else {
        // Target is outside map bounds, find intersection with map boundary
        // Use parametric line equation to find intersection
        double dx = point.x - sensor_origin.x;
        double dy = point.y - sensor_origin.y;

        // Calculate map boundaries in world coordinates
        double map_min_x = local_map_.info.origin.position.x;
        double map_max_x = local_map_.info.origin.position.x + map_width_ * map_resolution_;
        double map_min_y = local_map_.info.origin.position.y;
        double map_max_y = local_map_.info.origin.position.y + map_height_ * map_resolution_;

        // Find intersection parameter t (0 = sensor, 1 = target)
        double t_max = 1.0;

        if (dx != 0) {
            double t_x_min = (map_min_x - sensor_origin.x) / dx;
            double t_x_max = (map_max_x - sensor_origin.x) / dx;
            if (t_x_min > t_x_max) std::swap(t_x_min, t_x_max);
            if (t_x_max > 0 && t_x_max < t_max) t_max = t_x_max;
        }

        if (dy != 0) {
            double t_y_min = (map_min_y - sensor_origin.y) / dy;
            double t_y_max = (map_max_y - sensor_origin.y) / dy;
            if (t_y_min > t_y_max) std::swap(t_y_min, t_y_max);
            if (t_y_max > 0 && t_y_max < t_max) t_max = t_y_max;
        }

        // Calculate intersection point
        double intersect_x = sensor_origin.x + t_max * dx;
        double intersect_y = sensor_origin.y + t_max * dy;

        // Convert intersection to map coordinates
        world_to_map(intersect_x, intersect_y, end_x, end_y);
        has_obstacle = false;  // No obstacle at map boundary
    }

    // Bresenham's line algorithm for ray tracing
    int dx = abs(end_x - sensor_x);
    int dy = abs(end_y - sensor_y);
    int x_step = (sensor_x < end_x) ? 1 : -1;
    int y_step = (sensor_y < end_y) ? 1 : -1;
    int error = dx - dy;

    if (should_debug) {
        RCLCPP_INFO(this->get_logger(), "🔄 BRESENHAM: Start[%d,%d] -> End[%d,%d], Steps[%d,%d]",
                   sensor_x, sensor_y, end_x, end_y, x_step, y_step);
    }

    int x = sensor_x;
    int y = sensor_y;
    int cells_traced = 0;
    int cells_free = 0;
    int cells_occupied = 0;

    while (true) {
        // Check if current position is within map bounds
        if (x < 0 || x >= map_width_ || y < 0 || y >= map_height_) {
            if (should_debug && cells_traced < 3) {
                RCLCPP_WARN(this->get_logger(), "⚠️ BRESENHAM: Cell[%d,%d] outside bounds [0,%d)x[0,%d) - breaking",
                           x, y, map_width_, map_height_);
            }
            break;  // Reached map boundary
        }

        int index = get_map_index(x, y);
        cells_traced++;

        // Mark cells along the ray
        if (x != end_x || y != end_y) {
            update_cell_state(index, false);  // Free space
            cells_free++;
            if (should_debug && cells_traced <= 3) {
                RCLCPP_INFO(this->get_logger(), "🟢 FREE[%d]: Cell[%d,%d] index=%d",
                           cells_traced, x, y, index);
            }
        } else {
            if (has_obstacle) {
                update_cell_state(index, true);   // Occupied (only if target was in map)
                cells_occupied++;
                if (should_debug) {
                    RCLCPP_INFO(this->get_logger(), "🔴 OCCUPIED: Cell[%d,%d] index=%d",
                               x, y, index);
                }
            } else {
                update_cell_state(index, false);  // Free space at boundary
                cells_free++;
                if (should_debug) {
                    RCLCPP_INFO(this->get_logger(), "🟢 FREE_BOUNDARY: Cell[%d,%d] index=%d",
                               x, y, index);
                }
            }
            break;
        }

        // Bresenham step
        if (x == end_x && y == end_y) break;

        int error2 = 2 * error;
        if (error2 > -dy) {
            error -= dy;
            x += x_step;
        }
        if (error2 < dx) {
            error += dx;
            y += y_step;
        }
    }

    if (should_debug) {
        RCLCPP_INFO(this->get_logger(), "✅ RAY_COMPLETE: Traced %d cells (%d free, %d occupied)",
                   cells_traced, cells_free, cells_occupied);
    }
}

void LocalMapping::convert_states_to_occupancy_grid()
{
    // Debug: Count cell states before conversion
    int unknown_states = 0, free_states = 0, occupied_states = 0;
    for (const auto& state : cell_states_) {
        if (state == CellState::UNKNOWN) unknown_states++;
        else if (state == CellState::FREE) free_states++;
        else if (state == CellState::OCCUPIED) occupied_states++;
    }
    RCLCPP_INFO(this->get_logger(), "🔄 CONVERSION_DEBUG: cell_states_ has Unknown=%d, Free=%d, Occupied=%d",
                unknown_states, free_states, occupied_states);

    for (int i = 0; i < static_cast<int>(cell_states_.size()); ++i) {
        // Directly convert cell state to occupancy grid value
        map_data_[i] = static_cast<int8_t>(cell_states_[i]);
    }

    // Apply robot radius free area (pre-computed indices)
    apply_robot_radius_free_area();

    // Debug: Count map_data_ values after conversion
    int unknown_data = 0, free_data = 0, occupied_data = 0;
    for (const auto& value : map_data_) {
        if (value == -1) unknown_data++;
        else if (value == 0) free_data++;
        else if (value == 100) occupied_data++;
    }
    RCLCPP_INFO(this->get_logger(), "✅ CONVERSION_DEBUG: map_data_ has Unknown=%d, Free=%d, Occupied=%d",
                unknown_data, free_data, occupied_data);
}

void LocalMapping::reset_scan_observation_flags()
{
    std::fill(cell_observed_this_scan_.begin(), cell_observed_this_scan_.end(), false);
}

void LocalMapping::update_map_origin()
{
    // Get current robot position from subscribed pose
    std::lock_guard<std::mutex> lock(pose_mutex_);
    if (!pose_received_) {
        RCLCPP_DEBUG(this->get_logger(), "No pose received yet, keeping default map origin");
        return;
    }

    // Get robot position in map frame from current pose
    double robot_x = current_pose_stamped_.pose.position.x;
    double robot_y = current_pose_stamped_.pose.position.y;

    // Calculate new map origin to center the map on the robot position in map frame
    // Map origin is at bottom-left corner, so we offset by half map size to center on robot
    double new_origin_x = robot_x - map_size_ / 2.0;
    double new_origin_y = robot_y - map_size_ / 2.0;

    // For grid alignment with global map, snap origin to grid boundaries
    if (global_map_received_) {
        // Get global map resolution for grid alignment
        double global_res = global_map_.info.resolution;
        double global_origin_x = global_map_.info.origin.position.x;
        double global_origin_y = global_map_.info.origin.position.y;

        // Snap to global grid alignment
        new_origin_x = global_origin_x + std::floor((new_origin_x - global_origin_x) / global_res) * global_res;
        new_origin_y = global_origin_y + std::floor((new_origin_y - global_origin_y) / global_res) * global_res;
    }

    // Update map origin to be centered on robot position in map frame
    local_map_.info.origin.position.x = new_origin_x;
    local_map_.info.origin.position.y = new_origin_y;
    local_map_.info.origin.position.z = 0.0;
    // No rotation needed since we're in map frame
    local_map_.info.origin.orientation.x = 0.0;
    local_map_.info.origin.orientation.y = 0.0;
    local_map_.info.origin.orientation.z = 0.0;
    local_map_.info.origin.orientation.w = 1.0;
}

void LocalMapping::shift_map_states(double dx, double dy)
{
    // Safety check: ensure cell_states_ is properly initialized
    int total_cells = map_width_ * map_height_;
    if (cell_states_.size() != total_cells) {
        RCLCPP_WARN(this->get_logger(), "🔧 FIXING cell_states_ initialization: expected %d cells, got %zu. Reinitializing...",
                    total_cells, cell_states_.size());
        cell_states_.resize(total_cells, CellState::UNKNOWN);
    }

    // Calculate shift in grid cells
    int shift_x = static_cast<int>(std::round(dx / map_resolution_));
    int shift_y = static_cast<int>(std::round(dy / map_resolution_));

    // Add debug logging
    RCLCPP_DEBUG(this->get_logger(), "Shifting map states by dx=%.3f, dy=%.3f (shift_x=%d, shift_y=%d)",
                 dx, dy, shift_x, shift_y);

    // Create new state arrays - now guaranteed to have correct size
    std::vector<CellState> new_cell_states(cell_states_.size(), CellState::UNKNOWN);

    // Copy states from old positions to new positions
    // Logic: if map origin moves right (dx > 0), then content moves left relative to new origin
    // So old content at (x,y) should move to (x + shift_x, y + shift_y) in new map
    for (int y = 0; y < map_height_; ++y) {
        for (int x = 0; x < map_width_; ++x) {
            int old_index = y * map_width_ + x;

            // Calculate new position after shift
            // Fixed: content moves opposite to origin shift
            int new_x = x + shift_x;
            int new_y = y + shift_y;

            // Check if new position is within bounds
            if (new_x >= 0 && new_x < map_width_ && new_y >= 0 && new_y < map_height_) {
                int new_index = new_y * map_width_ + new_x;
                // Additional safety check for array bounds
                if (new_index >= 0 && new_index < static_cast<int>(new_cell_states.size()) &&
                    old_index >= 0 && old_index < static_cast<int>(cell_states_.size())) {
                    new_cell_states[new_index] = cell_states_[old_index];
                } else {
                    RCLCPP_ERROR(this->get_logger(), "🚨 Array bounds error: new_index=%d (max=%zu), old_index=%d (max=%zu)",
                                 new_index, new_cell_states.size(), old_index, cell_states_.size());
                }
            }
            // If out of bounds, the state is lost (becomes UNKNOWN in new map)
        }
    }

    // Replace old states with shifted states
    cell_states_ = std::move(new_cell_states);

    // Debug: count non-unknown cells
    int occupied_count = 0, free_count = 0, unknown_count = 0;
    for (const auto& state : cell_states_) {
        if (state == CellState::OCCUPIED) occupied_count++;
        else if (state == CellState::FREE) free_count++;
        else unknown_count++;
    }
    RCLCPP_DEBUG(this->get_logger(), "After shift: occupied=%d, free=%d, unknown=%d",
                 occupied_count, free_count, unknown_count);
}

pcl::PointXYZ LocalMapping::get_sensor_origin_in_map_frame()
{
    try {
        // Get sensor offset in base frame first (no mutex needed for TF operations)
        pcl::PointXYZ sensor_in_base;
        try {
            // Get transform from base_frame to lidar_frame
            geometry_msgs::msg::TransformStamped base_to_lidar_transform = tf_buffer_->lookupTransform(
                base_frame_id_,     // target frame (agv_base_link)
                lidar_frame_id_,    // source frame (radar_link)
                tf2::TimePointZero  // get latest available transform
            );

            // Transform a point at origin of radar_link to agv_base_link frame
            geometry_msgs::msg::PointStamped sensor_origin_lidar, sensor_origin_base;
            sensor_origin_lidar.header.frame_id = lidar_frame_id_;
            sensor_origin_lidar.point.x = 0.0;
            sensor_origin_lidar.point.y = 0.0;
            sensor_origin_lidar.point.z = 0.0;

            tf2::doTransform(sensor_origin_lidar, sensor_origin_base, base_to_lidar_transform);
            sensor_in_base = pcl::PointXYZ(sensor_origin_base.point.x, sensor_origin_base.point.y, sensor_origin_base.point.z);
        } catch (const tf2::TransformException& ex) {
            RCLCPP_WARN(this->get_logger(), "TF lookup failed for sensor origin: %s", ex.what());
            return pcl::PointXYZ(0.0, 0.0, 0.0);
        }

        // Get current robot position from pose (with mutex)
        geometry_msgs::msg::Pose current_pose;
        {
            std::lock_guard<std::mutex> lock(pose_mutex_);
            if (!pose_received_) {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                                    "No pose received, using default sensor origin");
                return pcl::PointXYZ(0.0, 0.0, 0.0);
            }
            current_pose = current_pose_stamped_.pose;
        }

        // Transform to map frame using current pose
        tf2::Transform base_to_map;
        tf2::fromMsg(current_pose, base_to_map);

        tf2::Vector3 sensor_base(sensor_in_base.x, sensor_in_base.y, sensor_in_base.z);
        tf2::Vector3 sensor_map = base_to_map * sensor_base;

        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                            "Sensor origin in %s frame: [%.6f, %.6f, %.6f]",
                            map_frame_id_.c_str(), sensor_map.x(), sensor_map.y(), sensor_map.z());

        return pcl::PointXYZ(sensor_map.x(), sensor_map.y(), sensor_map.z());

    } catch (const tf2::TransformException& ex) {
        RCLCPP_WARN(this->get_logger(), "TF lookup failed: %s", ex.what());
        return pcl::PointXYZ(0.0, 0.0, 0.0);
    }
}

void LocalMapping::initialize_layered_mapping()
{
    double height_range = height_max_ - height_min_;
    double robot_height = robot_traversable_max_ - robot_traversable_min_;  // Calculate robot height from range
    layered_mapper_ = std::make_unique<LayeredMapping>(
        map_size_, map_resolution_, layer_height_, robot_height, robot_radius_, height_range,
        hit_increment_, miss_increment_, free_threshold_, occupied_threshold_, 
        min_log_odds_, max_log_odds_);

    layered_mapper_->set_robot_parameters(robot_height, 0.0);  // No separate clearance parameter

    RCLCPP_INFO(this->get_logger(), "Initialized 2.5D layered mapping");
    RCLCPP_INFO(this->get_logger(), "Layer height: %.3f m, Robot traversable range: [%.3f, %.3f] m",
                layer_height_, robot_traversable_min_, robot_traversable_max_);
}

void LocalMapping::update_layered_map_with_pointcloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud)
{
    if (!layered_mapper_) {
        RCLCPP_ERROR(this->get_logger(), "Layered mapper not initialized");
        return;
    }

    if (!cloud) {
        RCLCPP_ERROR(this->get_logger(), "Input cloud is null");
        return;
    }

    if (cloud->empty()) {
        RCLCPP_WARN(this->get_logger(), "Input cloud is empty");
        return;
    }

    // Get sensor origin in map frame (to match the transformed point cloud)
    auto sensor_origin_start = std::chrono::steady_clock::now();
    pcl::PointXYZ sensor_origin = get_sensor_origin_in_map_frame();
    auto sensor_origin_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - sensor_origin_start).count() / 1000.0;

    RCLCPP_INFO(this->get_logger(),
               "[LAYERED_MAPPING] Updating 2.5D map with %zu points, sensor at [%.3f, %.3f, %.3f] in %s frame ⏱️ SensorOrigin: %.3fms",
               cloud->size(), sensor_origin.x, sensor_origin.y, sensor_origin.z, map_frame_id_.c_str(), sensor_origin_time);

    // Update layered map (both points and sensor origin are now in map frame)
    auto layered_update_start = std::chrono::steady_clock::now();
    try {
        // 根据持久化设置选择更新方法
        if (layered_mapper_->is_persistence_enabled()) {
            RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ⏱️ Starting persistent layered mapping update...");
            auto persistent_start = std::chrono::steady_clock::now();
            try {
                layered_mapper_->update_persistent_layered_map(cloud, sensor_origin);
                auto persistent_time = std::chrono::duration_cast<std::chrono::microseconds>(
                    std::chrono::steady_clock::now() - persistent_start).count() / 1000.0;
                RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ✅ update_persistent_layered_map completed: %.3fms", persistent_time);
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] ❌ Exception in update_persistent_layered_map: %s", e.what());
                throw;
            } catch (...) {
                RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] ❌ Unknown exception in update_persistent_layered_map");
                throw;
            }
        } else {
            RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ⏱️ Starting standard layered mapping update...");
            auto standard_start = std::chrono::steady_clock::now();
            try {
                layered_mapper_->update_layered_map(cloud, sensor_origin);
                auto standard_time = std::chrono::duration_cast<std::chrono::microseconds>(
                    std::chrono::steady_clock::now() - standard_start).count() / 1000.0;
                RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ✅ update_layered_map completed: %.3fms", standard_time);
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] ❌ Exception in update_layered_map: %s", e.what());
                throw;
            } catch (...) {
                RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] ❌ Unknown exception in update_layered_map");
                throw;
            }
        }
        auto layered_update_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - layered_update_start).count() / 1000.0;
        RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ⏱️ Total layered map update completed in %.3f ms", layered_update_time);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] Exception in layered_mapper update: %s", e.what());
        return;
    } catch (...) {
        RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] Unknown exception in layered_mapper update");
        return;
    }

    // Convert to 2D occupancy grid
    auto conversion_start = std::chrono::steady_clock::now();
    try {
        RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] Converting layered map to 2D occupancy grid");
        convert_layered_to_2d_map();
        auto conversion_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - conversion_start).count() / 1000.0;
        RCLCPP_INFO(this->get_logger(), "[LAYERED_MAPPING] ⏱️ convert_layered_to_2d_map completed in %.3f ms", conversion_time);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] Exception in convert_layered_to_2d_map: %s", e.what());
        return;
    } catch (...) {
        RCLCPP_ERROR(this->get_logger(), "[LAYERED_MAPPING] Unknown exception in convert_layered_to_2d_map");
        return;
    }
}

void LocalMapping::convert_layered_to_2d_map()
{
    if (!layered_mapper_) {
        return;
    }

    auto conversion_start = std::chrono::steady_clock::now();
    std::lock_guard<std::mutex> lock(map_mutex_);

    // Update map origin (for robot-centered maps, this is constant)
    auto step_start = std::chrono::steady_clock::now();
    update_map_origin();
    auto origin_update_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;

    // Sync layered mapper's origin with local map origin
    step_start = std::chrono::steady_clock::now();
    layered_mapper_->set_map_origin(local_map_.info.origin.position.x,
                                   local_map_.info.origin.position.y);
    auto sync_origin_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;

    RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                        "Converting layered map to 2D, origin: [%.3f, %.3f] in %s frame",
                        local_map_.info.origin.position.x, local_map_.info.origin.position.y,
                        map_frame_id_.c_str());

    // Get 2D occupancy grid from layered mapper (based on current frame)
    step_start = std::chrono::steady_clock::now();
    pcl::PointCloud<pcl::PointXYZ>::Ptr current_cloud;
    pcl::PointXYZ sensor_origin;

    // 获取当前帧的点云和传感器位置（确保使用map frame坐标系）
    {
        std::lock_guard<std::mutex> lock(pointcloud_mutex_);
        if (latest_transformed_cloud_) {
            current_cloud = latest_transformed_cloud_;  // 使用已变换到map frame的点云
            sensor_origin = get_sensor_origin_in_map_frame();  // 修复：使用map frame保持坐标系一致
            RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] Using latest_transformed_cloud with %zu points", 
                        current_cloud->size());
        } else {
            RCLCPP_WARN(this->get_logger(), "[LAYERED_TO_2D] No latest_transformed_cloud available");
        }
    }

    std::vector<int8_t> occupancy_data;
    if (layered_mapper_->is_persistence_enabled()) {
        // 使用持久化的submap数据生成2D地图
        RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] Using persistent occupancy grid");
        occupancy_data = layered_mapper_->get_2d_occupancy_grid_persistent();
    } else if (current_cloud && !current_cloud->empty()) {
        // 基于当前帧生成2D地图（不持久化layer状态）
        RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] Using current frame with %zu points", current_cloud->size());
        occupancy_data = layered_mapper_->get_2d_occupancy_grid(current_cloud, sensor_origin);
    } else {
        // 没有当前帧数据，返回空地图
        RCLCPP_WARN(this->get_logger(), "[LAYERED_TO_2D] No current frame data, returning empty map");
        occupancy_data.resize(map_width_ * map_height_, -1);
    }
    auto layered_conversion_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;

    // Fuse with global map if enabled
    step_start = std::chrono::steady_clock::now();
    if (enable_global_fusion_) {
        RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] Starting global map fusion");
        fuse_with_global_map(occupancy_data);
    } else {
        RCLCPP_DEBUG(this->get_logger(), "[LAYERED_TO_2D] Global map fusion disabled");
    }
    auto fusion_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;
    RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] ⏱️ Global map fusion took %.3f ms", fusion_time);

    // Count different cell types for debugging
    step_start = std::chrono::steady_clock::now();
    int unknown_count = 0, free_count = 0, occupied_count = 0;
    for (auto value : occupancy_data) {
        if (value == -1) unknown_count++;
        else if (value == 0) free_count++;
        else if (value == 100) occupied_count++;
    }
    auto stats_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;

    // Copy to local map data
    step_start = std::chrono::steady_clock::now();
    auto copy_start = std::chrono::steady_clock::now();
    std::copy(occupancy_data.begin(), occupancy_data.end(), map_data_.begin());
    auto copy1_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - copy_start).count() / 1000.0;

    // Apply robot radius free area (pre-computed indices) - skip for layered mapping as it handles footprint clearing
    auto robot_radius_start = std::chrono::steady_clock::now();
    // Note: Layered mapping already handles robot footprint clearing in submaps, so skip this aggressive clearing
    // apply_robot_radius_free_area();  // Disabled for layered mapping to preserve unknown areas
    auto robot_radius_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - robot_radius_start).count() / 1000.0;

    auto copy2_start = std::chrono::steady_clock::now();
    std::copy(map_data_.begin(), map_data_.end(), local_map_.data.begin());
    auto copy2_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - copy2_start).count() / 1000.0;
    
    auto copy_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - step_start).count() / 1000.0;
    
    RCLCPP_INFO(this->get_logger(), "[LAYERED_TO_2D] ⏱️ Copy operations: Total=%.3fms [Copy1=%.3f, RobotRadius=%.3f, Copy2=%.3f]",
                copy_time, copy1_time, robot_radius_time, copy2_time);

    auto total_conversion_time = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now() - conversion_start).count() / 1000.0;

    RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                         "🏗️ 2.5D→2D conversion: Total=%.3fms [Origin=%.3f, Sync=%.3f, Convert=%.3f, Fusion=%.3f, Stats=%.3f, Copy=%.3f]",
                         total_conversion_time, origin_update_time, sync_origin_time, layered_conversion_time,
                         fusion_time, stats_time, copy_time);

    RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                        "2D map stats (after fusion): unknown=%d, free=%d, occupied=%d",
                        unknown_count, free_count, occupied_count);

    RCLCPP_DEBUG(this->get_logger(), "Converted layered map to 2D occupancy grid");
}

void LocalMapping::fuse_with_global_map(std::vector<int8_t>& local_occupancy_grid)
{
    if (!global_map_received_) {
        RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                             "Global map not received, skipping fusion");
        return;
    }

    auto fusion_start = std::chrono::steady_clock::now();

    // 高效的批量融合算法
    bool efficient_success = fuse_with_global_map_efficient(local_occupancy_grid);

    auto fusion_end = std::chrono::steady_clock::now();
    auto fusion_duration = std::chrono::duration_cast<std::chrono::microseconds>(fusion_end - fusion_start);

    if (efficient_success) {
        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                            "✅ Efficient global fusion completed in %.3f ms",
                            fusion_duration.count() / 1000.0);
    } else {
        RCLCPP_INFO(this->get_logger(), "❌ Efficient global fusion failed");
    }
}

bool LocalMapping::fuse_with_global_map_efficient(std::vector<int8_t>& local_occupancy_grid)
{
    try {
        auto step_start = std::chrono::steady_clock::now();

        // 步骤1: 由于局部地图现在也是在map坐标系下，不需要TF变换
        // 直接计算局部地图在map坐标系中的边界框
        double local_corners_x[4] = {
            local_map_.info.origin.position.x,                                           // 左下
            local_map_.info.origin.position.x + map_width_ * map_resolution_,           // 右下
            local_map_.info.origin.position.x + map_width_ * map_resolution_,           // 右上
            local_map_.info.origin.position.x                                            // 左上
        };
        double local_corners_y[4] = {
            local_map_.info.origin.position.y,                                           // 左下
            local_map_.info.origin.position.y,                                           // 右下
            local_map_.info.origin.position.y + map_height_ * map_resolution_,          // 右上
            local_map_.info.origin.position.y + map_height_ * map_resolution_           // 左上
        };

        // 局部地图边界框（已经在map坐标系中）
        double min_map_x = local_corners_x[0];  // 左边界
        double max_map_x = local_corners_x[1];  // 右边界
        double min_map_y = local_corners_y[0];  // 下边界
        double max_map_y = local_corners_y[2];  // 上边界

        auto boundary_calc_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        // 步骤2: 计算在全局地图中的重叠区域
        int global_start_x = std::max(0, static_cast<int>(std::floor((min_map_x - global_map_.info.origin.position.x) / global_map_.info.resolution)));
        int global_end_x = std::min(static_cast<int>(global_map_.info.width) - 1,
                                   static_cast<int>(std::ceil((max_map_x - global_map_.info.origin.position.x) / global_map_.info.resolution)));
        int global_start_y = std::max(0, static_cast<int>(std::floor((min_map_y - global_map_.info.origin.position.y) / global_map_.info.resolution)));
        int global_end_y = std::min(static_cast<int>(global_map_.info.height) - 1,
                                   static_cast<int>(std::ceil((max_map_y - global_map_.info.origin.position.y) / global_map_.info.resolution)));

        // 检查是否有重叠
        if (global_start_x > global_end_x || global_start_y > global_end_y) {
            RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                                 "No overlap between local and global map");
            return true; // 没有重叠不是错误，直接返回
        }

        // 步骤3: 批量处理重叠区域
        auto fusion_start = std::chrono::steady_clock::now();
        int fusion_count = 0;
        int unknown_to_global = 0, free_to_occupied = 0, occupied_priority = 0;

        // 在重叠区域内进行融合
        for (int global_y = global_start_y; global_y <= global_end_y; ++global_y) {
            for (int global_x = global_start_x; global_x <= global_end_x; ++global_x) {
                // 全局地图坐标转换为map世界坐标
                double map_world_x = global_map_.info.origin.position.x + (global_x + 0.5) * global_map_.info.resolution;
                double map_world_y = global_map_.info.origin.position.y + (global_y + 0.5) * global_map_.info.resolution;

                // 直接转换为局部地图像素坐标（都在map坐标系中）
                int local_x = static_cast<int>(std::floor((map_world_x - local_map_.info.origin.position.x) / local_map_.info.resolution));
                int local_y = static_cast<int>(std::floor((map_world_y - local_map_.info.origin.position.y) / local_map_.info.resolution));

                // 检查是否在局部地图边界内
                if (local_x < 0 || local_x >= map_width_ || local_y < 0 || local_y >= map_height_) {
                    continue;
                }

                // 获取局部地图和全局地图的值
                int local_index = local_y * map_width_ + local_x;
                int global_index = global_y * global_map_.info.width + global_x;

                if (local_index >= static_cast<int>(local_occupancy_grid.size()) ||
                    global_index >= static_cast<int>(global_map_.data.size())) {
                    continue;
                }

                int8_t local_sensor_value = local_occupancy_grid[local_index];
                int8_t global_value = global_map_.data[global_index];

                // 应用融合规则
                int8_t fused_value = apply_fusion_rule(local_sensor_value, global_value);

                // 统计融合类型
                if (local_sensor_value != fused_value) {
                    fusion_count++;
                    if (local_sensor_value == -1) {
                        unknown_to_global++;
                    } else if (local_sensor_value == 0 && global_value > 0) {
                        free_to_occupied++;
                    } else if (local_sensor_value > 0) {
                        occupied_priority++;
                    }
                }

                // 更新融合后的值
                local_occupancy_grid[local_index] = fused_value;
            }
        }

        auto fusion_process_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - fusion_start).count() / 1000.0;

        auto total_time = boundary_calc_time + fusion_process_time;

        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                             "🔧 Efficient fusion performance (MAP FRAME): Total=%.3fms [Boundary=%.3fms, Fusion=%.3fms]",
                             total_time, boundary_calc_time, fusion_process_time);

        RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                             "Efficient global map fusion: %d cells modified (unknown→global:%d, free→occupied:%d, occupied_priority:%d)",
                             fusion_count, unknown_to_global, free_to_occupied, occupied_priority);

        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 3000,
                             "Fusion stats: Boundary calc: %.2fms, Fusion: %.2fms. Fused: %d cells. Overlap: %dx%d",
                             boundary_calc_time, fusion_process_time, fusion_count,
                             (global_end_x - global_start_x + 1), (global_end_y - global_start_y + 1));

        return true;

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "Error in efficient global map fusion: %s", e.what());
        return false;
    }
}

// 老的逐像素融合方法已删除，现在只使用高效的批量融合方法

int8_t LocalMapping::apply_fusion_rule(int8_t local_value, int8_t global_value)
{
    // 规则1: 局部UNKNOWN + 全局任意 → 使用特殊标记值标识全局融合的障碍物
    if (local_value == -1) {
        if (global_value > 0) {
            return global_fusion_marker_value_;  // 使用特殊标记值标识来自全局的障碍物
        }
        return global_value;  // FREE或UNKNOWN直接返回
    }

    // 规则2: 局部OCCUPIED + 全局任意 → OCCUPIED (局部观测优先级最高)
    if (local_value > 0) {
        return 100;  // 标准化为100，局部观测的障碍物
    }

    // 规则3: 局部FREE + 全局OCCUPIED → 根据保守策略决定
    if (local_value == 0 && global_value > 0) {
        if (global_fusion_conservative_) {
            return global_fusion_marker_value_;  // 保守模式：使用特殊标记值保留全局障碍物
        } else {
            return 0;    // 激进模式：信任局部观测
        }
    }

    // 规则4: 局部FREE + 全局FREE/UNKNOWN → FREE
    if (local_value == 0 && (global_value == 0 || global_value == -1)) {
        return 0;
    }

    // 默认返回局部值
    return local_value;
}

int8_t LocalMapping::get_global_map_value(int local_x, int local_y)
{
    if (!global_map_received_) {
        return -1;  // 未知
    }

    // 步骤1: 将局部地图坐标转换为map坐标系下的坐标（局部地图现在也在map坐标系中）
    double map_x = local_map_.info.origin.position.x + (local_x + 0.5) * local_map_.info.resolution;
    double map_y = local_map_.info.origin.position.y + (local_y + 0.5) * local_map_.info.resolution;

    // 步骤2: 将map坐标系坐标转换为全局地图像素坐标
    int global_x, global_y;
    if (!world_to_global_map(map_x, map_y, global_x, global_y)) {
        return -1;  // 超出全局地图范围
    }

    // 步骤3: 获取全局地图值
    int global_index = global_y * global_map_.info.width + global_x;
    if (global_index >= 0 && global_index < static_cast<int>(global_map_.data.size())) {
        return global_map_.data[global_index];
    }

    return -1;  // 默认未知
}

bool LocalMapping::world_to_global_map(double world_x, double world_y, int& global_x, int& global_y) const
{
    if (!global_map_received_) {
        return false;
    }

    // 转换为全局地图坐标
    // 使用floor确保正确的像素对应关系
    global_x = static_cast<int>(std::floor((world_x - global_map_.info.origin.position.x) / global_map_.info.resolution));
    global_y = static_cast<int>(std::floor((world_y - global_map_.info.origin.position.y) / global_map_.info.resolution));

    // 检查边界
    return (global_x >= 0 && global_x < static_cast<int>(global_map_.info.width) &&
            global_y >= 0 && global_y < static_cast<int>(global_map_.info.height));
}

void LocalMapping::map_update_thread_function()
{
    rclcpp::Rate rate(map_update_rate_);
    RCLCPP_INFO(this->get_logger(), "[UPDATE_THREAD] Map update thread started at %.1f Hz", map_update_rate_);
    
    int loop_count = 0;
    while (!should_stop_update_thread_ && rclcpp::ok()) {
        loop_count++;
        
        // Log thread activity every 25 loops (roughly every 2.5 seconds at 10Hz) 
        if (loop_count % 25 == 0) {
            size_t cloud_queue_size, pose_queue_size;
            {
                std::lock_guard<std::mutex> pc_lock(pointcloud_mutex_);
                std::lock_guard<std::mutex> pose_lock(pose_mutex_);
                cloud_queue_size = cloud_queue_.size();
                pose_queue_size = pose_queue_.size();
            }
            RCLCPP_INFO(this->get_logger(), 
                       "[UPDATE_THREAD] ✅ Status check - Cloud queue: %zu, Pose queue: %zu, Loop: %d",
                       cloud_queue_size, pose_queue_size, loop_count);
        }
        
        // Log if thread seems stuck
        if (loop_count > 0 && loop_count % 100 == 0) {
            RCLCPP_WARN(this->get_logger(), "[UPDATE_THREAD] ⚠️ Thread running for %d loops - check for performance issues", loop_count);
        }
        
        try {
            // Pre-clean queues before processing to prevent excessive length and remove old data
            cleanup_queues_before_processing();
            process_latest_data();
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "[UPDATE_THREAD] ❌ Exception in main loop: %s", e.what());
        } catch (...) {
            RCLCPP_ERROR(this->get_logger(), "[UPDATE_THREAD] ❌ Unknown exception in main loop");
        }
        rate.sleep();
    }
    RCLCPP_INFO(this->get_logger(), "[UPDATE_THREAD] Map update thread stopped");
}

void LocalMapping::cleanup_queues_before_processing()
{
    std::lock_guard<std::mutex> pc_lock(pointcloud_mutex_);
    std::lock_guard<std::mutex> pose_lock(pose_mutex_);

    const double MAX_TIME_DIFF = 5.0; // seconds - increased for debugging timestamp sync issues
    const size_t MAX_QUEUE_SIZE = 100;

    // Use relative time comparison instead of wall clock
    // Get the most recent pose timestamp for comparison
    double reference_time_sec = 0.0;
    if (!pose_queue_.empty()) {
        reference_time_sec = pose_queue_.back().stamp.seconds();
    }

    // Clean old pointclouds relative to the most recent pose
    while (!cloud_queue_.empty()) {
        auto cloud_msg = cloud_queue_.front();
        double cloud_time_sec = stamp_to_double(cloud_msg->header.stamp);

        // Calculate age relative to most recent pose timestamp
        double age = reference_time_sec - cloud_time_sec;

        if (reference_time_sec > 0 && age > MAX_TIME_DIFF) {
            auto current_time_sec = this->now().seconds();
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                                "[CLEANUP] Discarding old pointcloud: age=%.3f sec (cloud_time=%.3f, latest_pose=%.3f, current_time=%.3f)", 
                                age, cloud_time_sec, reference_time_sec, current_time_sec);
            cloud_queue_.pop_front();
        } else {
            break; // Rest are newer or no reference time available
        }
    }

    // Limit pointcloud queue size
    while (cloud_queue_.size() > MAX_QUEUE_SIZE) {
        RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                            "[CLEANUP] Pointcloud queue too long (%zu), discarding oldest", cloud_queue_.size());
        cloud_queue_.pop_front();
    }

    // Clean old poses (keep reasonable history)
    while (pose_queue_.size() > 100) {
        RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                             "[CLEANUP] Pose queue too long (%zu), discarding oldest", pose_queue_.size());
        pose_queue_.pop_front();
    }
}

void LocalMapping::process_latest_data()
{
    // Copy queues locally to avoid holding locks for too long
    std::deque<sensor_msgs::msg::PointCloud2::ConstSharedPtr> local_cloud_queue;
    std::deque<TimedPose> local_pose_queue;

    {
        std::lock_guard<std::mutex> pc_lock(pointcloud_mutex_);
        std::lock_guard<std::mutex> pose_lock(pose_mutex_);

        if (cloud_queue_.empty() || pose_queue_.empty()) {
            RCLCPP_DEBUG_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                                "🔍 QUEUE_CHECK: cloud_queue_.size()=%zu, pose_queue_.size()=%zu",
                                cloud_queue_.size(), pose_queue_.size());
            return;
        }

        // Copy queues locally
        local_cloud_queue = cloud_queue_;
        local_pose_queue = pose_queue_;
        
        RCLCPP_INFO(this->get_logger(),
                   "🔍 QUEUE_STATUS: cloud_queue=%zu, pose_queue=%zu",
                   local_cloud_queue.size(), local_pose_queue.size());
    }

    // Process oldest pointcloud that can be interpolated
    if (!local_cloud_queue.empty()) {
        auto cloud_msg = local_cloud_queue.front();
        double cloud_time_sec = stamp_to_double(cloud_msg->header.stamp);

        // Log detailed timestamp information
        RCLCPP_INFO(this->get_logger(),
                   "🔍 TIMESTAMP_CHECK: cloud_time=%.6f, pose_queue_size=%zu",
                   cloud_time_sec, local_pose_queue.size());

        // Check if we have enough pose data to interpolate for this pointcloud
        if (local_pose_queue.size() < 2) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 2000,
                                "❌ Not enough pose data for interpolation (need at least 2, have %zu)",
                                local_pose_queue.size());
            return; // Wait for more pose data
        }

        // Check if pointcloud timestamp is within pose data range
        double oldest_pose_time_sec = local_pose_queue.front().stamp.seconds();
        double newest_pose_time_sec = local_pose_queue.back().stamp.seconds();
        
        RCLCPP_INFO(this->get_logger(),
                   "🔍 TIME_RANGE: cloud=%.6f, poses=[%.6f -> %.6f] (span=%.3fs)",
                   cloud_time_sec, oldest_pose_time_sec, newest_pose_time_sec,
                   newest_pose_time_sec - oldest_pose_time_sec);

        if (cloud_time_sec < oldest_pose_time_sec) {
            // Check if pointcloud is within acceptable time range for extrapolation
            double time_gap = oldest_pose_time_sec - cloud_time_sec;
            if (time_gap > max_time_diff_) {
                RCLCPP_WARN(this->get_logger(),
                           "❌ CLOUD_TOO_OLD: cloud_time=%.6f < oldest_pose=%.6f (diff=%.3fs > max_diff=%.1fs) - SKIPPING",
                           cloud_time_sec, oldest_pose_time_sec, time_gap, max_time_diff_);
                return;
            } else {
                // Allow extrapolation for slightly old pointclouds (common in rosbag playback)
                RCLCPP_INFO(this->get_logger(),
                           "🔄 EXTRAPOLATING: cloud_time=%.6f < oldest_pose=%.6f (diff=%.3fs) - Using oldest pose",
                           cloud_time_sec, oldest_pose_time_sec, time_gap);
            }
        }

        if (cloud_time_sec > newest_pose_time_sec) {
            // Pointcloud is too new, wait for more pose data
            double time_diff = cloud_time_sec - newest_pose_time_sec;
            if (time_diff > 0.2) { // If waiting more than 200ms, something might be wrong
                RCLCPP_WARN(this->get_logger(),
                           "❌ CLOUD_TOO_NEW: cloud_time=%.6f > newest_pose=%.6f (diff=%.3fs) - Large gap!",
                           cloud_time_sec, newest_pose_time_sec, time_diff);
            } else {
                RCLCPP_INFO(this->get_logger(),
                           "⏳ WAITING_FOR_POSE: cloud_time=%.6f > newest_pose=%.6f (diff=%.3fs)",
                           cloud_time_sec, newest_pose_time_sec, time_diff);
            }
            return; // Wait for more pose data
        }

        // We can interpolate/extrapolate for this pointcloud
        geometry_msgs::msg::Pose interp_pose;
        
        // Handle extrapolation for old pointclouds (use oldest pose)
        if (cloud_time_sec < oldest_pose_time_sec) {
            interp_pose = local_pose_queue.front().pose;
            RCLCPP_INFO(this->get_logger(), "🔄 Using oldest pose for extrapolation");
        } else if (!get_interpolated_pose_from_local_queue(cloud_msg->header.stamp, local_pose_queue, interp_pose)) {
            RCLCPP_WARN(this->get_logger(), "❌ Failed to interpolate pose for pointcloud - SKIPPING");
            return;
        }

        // Successfully found interpolated pose, process this pointcloud
        auto pcd_time = stamp_to_double(cloud_msg->header.stamp);

        // Find the exact poses used for interpolation
        std::string pose_info = "UNKNOWN";
        if (local_pose_queue.size() >= 2) {
            // Find the poses used for interpolation
            auto it = std::lower_bound(local_pose_queue.begin(), local_pose_queue.end(), pcd_time,
                [](const TimedPose& pose, double t) {
                    return pose.stamp.seconds() < t;
                });

            if (it != local_pose_queue.begin() && it != local_pose_queue.end()) {
                const auto &p1 = *it;      // pose >= target_time
                const auto &p0 = *(it-1);  // pose < target_time
                pose_info = "interpolated between [" + std::to_string(p0.stamp.seconds()) +
                           " - " + std::to_string(p1.stamp.seconds()) + "]";
            } else if (it == local_pose_queue.begin()) {
                pose_info = "exact match at " + std::to_string(it->stamp.seconds());
            }
        }

        auto current_time = this->now().seconds();
        RCLCPP_INFO(this->get_logger(),
                   "[PROCESS_DATA] 🗺️ PROCESSING MAP FRAME: Lidar[%.6f] + Poses[%s] -> Map update [Current: %.6f]",
                   pcd_time, pose_info.c_str(), current_time);

        // Start timing the entire processing pipeline
        auto process_start = std::chrono::steady_clock::now();

        // Step 1: Convert to PCL
        auto step_start = std::chrono::steady_clock::now();
        pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
        pcl::fromROSMsg(*cloud_msg, *cloud);
        auto convert_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        // Step 2: Filter pointcloud
        step_start = std::chrono::steady_clock::now();
        auto filtered = filter_pointcloud(cloud);
        auto filter_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        RCLCPP_INFO(this->get_logger(),
                   "[PROCESS_DATA] 🔍 FILTERING: %zu -> %zu points (height: %.1f-%.1f, robot_radius: %.2f, skip_every: %d) ⏱️ %.3fms",
                   cloud->size(), filtered->size(), height_min_, height_max_, robot_radius_, point_skip_filter_, filter_time);

        if (!filtered || filtered->empty()) {
            RCLCPP_WARN(this->get_logger(),
                       "[PROCESS_DATA] ❌ Pointcloud empty after filtering - no obstacles to map! Original: %zu points",
                       cloud ? cloud->size() : 0);
            // Mark processed and return
            {
                std::lock_guard<std::mutex> lock(pointcloud_mutex_);
                if (!cloud_queue_.empty() && cloud_queue_.front() == cloud_msg) {
                    cloud_queue_.pop_front();
                }
            }
            return;
        }

        // Step 3: Transform to map frame using our interpolated pose
        step_start = std::chrono::steady_clock::now();
        auto cloud_map = transform_pointcloud_to_map_with_pose(filtered, interp_pose);
        RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] Transformed %zu -> %zu points to map frame", 
                   filtered ? filtered->size() : 0, cloud_map ? cloud_map->size() : 0);
        auto transform_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        // Step 4: Apply height filtering in map coordinate system
        step_start = std::chrono::steady_clock::now();
        if (cloud_map) {
            cloud_map = filter_pointcloud_height_in_map(cloud_map);
            RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] Height filtered -> %zu points in map frame", 
                       cloud_map ? cloud_map->size() : 0);
        }
        auto height_filter_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        if (!cloud_map) {
            RCLCPP_ERROR_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                                 "❌ Transformed pointcloud is null!");
            // Mark processed and return
            {
                std::lock_guard<std::mutex> lock(pointcloud_mutex_);
                if (!cloud_queue_.empty() && cloud_queue_.front() == cloud_msg) {
                    cloud_queue_.pop_front();
                }
            }
            return;
        }

        // Store the transformed point cloud for convert_layered_to_2d_map to use
        {
            std::lock_guard<std::mutex> lock(pointcloud_mutex_);
            latest_transformed_cloud_ = cloud_map;  // Store map frame cloud
            RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] Stored latest_transformed_cloud with %zu points", 
                        cloud_map ? cloud_map->size() : 0);
        }

        // Publish transformed point cloud for debugging (non-blocking)
        if (transformed_cloud_publisher_->get_subscription_count() > 0) {
            sensor_msgs::msg::PointCloud2 transformed_msg;
            pcl::toROSMsg(*cloud_map, transformed_msg);
            transformed_msg.header.stamp = cloud_msg->header.stamp;
            transformed_msg.header.frame_id = map_frame_id_;
            transformed_cloud_publisher_->publish(transformed_msg);
        }

        // Step 5: Update map center for rolling maps
        step_start = std::chrono::steady_clock::now();
        double dx = interp_pose.position.x - map_center_x_;
        double dy = interp_pose.position.y - map_center_y_;
        if (std::fabs(dx) >= map_resolution_ || std::fabs(dy) >= map_resolution_) {
            if (use_layered_mapping_) {
                // For layered mapping, update the origin directly
                if (layered_mapper_) {
                    layered_mapper_->set_map_origin(interp_pose.position.x, interp_pose.position.y);
                }
            } else {
                // For direct mapping, shift cell states
                shift_map_states(dx, dy);
            }
            map_center_x_ = interp_pose.position.x;
            map_center_y_ = interp_pose.position.y;
        }
        auto shift_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        // Step 6: Integrate into map using appropriate method
        step_start = std::chrono::steady_clock::now();
        try {
            if (use_layered_mapping_) {
                RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] Using 2.5D layered mapping with %zu points", cloud_map->size());
                // Use 2.5D layered mapping
                update_layered_map_with_pointcloud(cloud_map);
            } else {
                RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] Using 2D direct mapping with %zu points", cloud_map->size());
                // Use 2D direct mapping
                update_map_origin();
                integrate_pointcloud(cloud_map);
                convert_states_to_occupancy_grid();
                std::copy(map_data_.begin(), map_data_.end(), local_map_.data.begin());
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "[PROCESS_DATA] Exception in map integration: %s", e.what());
            // Still mark as processed to avoid getting stuck
        }
        auto integration_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - step_start).count() / 1000.0;

        // Calculate total processing time
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now() - process_start).count() / 1000.0;

        // Log performance statistics  
        RCLCPP_INFO_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                             "⏱️ Processing times: Total=%.3fms [Convert=%.3f, Filter=%.3f, Transform=%.3f, HeightFilter=%.3f, Shift=%.3f, Integration=%.3f]",
                             total_time, convert_time, filter_time, transform_time, height_filter_time, shift_time, integration_time);

        RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] ✅ Processing completed successfully");
        
        // Mark this pointcloud as processed for cleanup
        {
            std::lock_guard<std::mutex> lock(pointcloud_mutex_);
            if (!cloud_queue_.empty() && cloud_queue_.front() == cloud_msg) {
                cloud_queue_.pop_front();
                RCLCPP_INFO(this->get_logger(), "[PROCESS_DATA] ✅ Removed processed cloud from queue, remaining: %zu", cloud_queue_.size());
            }
        }
    }
}

bool LocalMapping::get_interpolated_pose(const builtin_interfaces::msg::Time& target_stamp, geometry_msgs::msg::Pose &out_pose)
{
    std::lock_guard<std::mutex> lock(pose_mutex_);
    if (pose_queue_.size() < 2) return false;
    rclcpp::Time t_target(target_stamp);
    // ensure queue sorted
    if (t_target < pose_queue_.front().stamp || t_target > pose_queue_.back().stamp) return false;
    // binary search
    size_t lo=0, hi=pose_queue_.size()-1;
    while (hi-lo>1) {
        size_t mid=(lo+hi)/2;
        if (pose_queue_[mid].stamp <= t_target) lo=mid; else hi=mid;
    }
    const auto &p0=pose_queue_[lo];
    const auto &p1=pose_queue_[hi];
    double dt = p1.stamp.seconds() - p0.stamp.seconds();
    if (dt==0) { out_pose=p0.pose; return true; }
    double alpha = (t_target.seconds() - p0.stamp.seconds())/dt;
    out_pose.position.x = p0.pose.position.x + alpha*(p1.pose.position.x - p0.pose.position.x);
    out_pose.position.y = p0.pose.position.y + alpha*(p1.pose.position.y - p0.pose.position.y);
    out_pose.position.z = p0.pose.position.z + alpha*(p1.pose.position.z - p0.pose.position.z);
    tf2::Quaternion q0,q1;
    tf2::fromMsg(p0.pose.orientation,q0);
    tf2::fromMsg(p1.pose.orientation,q1);
    tf2::Quaternion q_interp = q0.slerp(q1, alpha);
    out_pose.orientation = tf2::toMsg(q_interp);
    return true;
}

bool LocalMapping::get_interpolated_pose_from_local_queue(const builtin_interfaces::msg::Time& target_stamp, const std::deque<TimedPose>& local_pose_queue, geometry_msgs::msg::Pose& out_pose)
{
    if (local_pose_queue.size() < 2) {
        RCLCPP_WARN(this->get_logger(), "🔍 POSE_DEBUG: Queue size insufficient: %zu poses", local_pose_queue.size());
        return false;
    }

    double target_time = stamp_to_double(target_stamp);

    // ensure target is within range
    double front_time = local_pose_queue.front().stamp.seconds();
    double back_time = local_pose_queue.back().stamp.seconds();

    RCLCPP_DEBUG(this->get_logger(), "🔍 POSE_DEBUG: Target time: %.6f, Queue range: [%.6f, %.6f]",
                target_time, front_time, back_time);

    if (target_time < front_time || target_time > back_time) {
        RCLCPP_WARN(this->get_logger(), "🔍 POSE_DEBUG: Target time outside queue range!");
        return false;
    }

    // Use lower_bound to find the first pose >= target_time
    auto it = std::lower_bound(local_pose_queue.begin(), local_pose_queue.end(), target_time,
        [](const TimedPose& pose, double t) {
            return pose.stamp.seconds() < t;
        });

    if (it == local_pose_queue.begin()) {
        // target_time equals the first pose time
        out_pose = it->pose;
        RCLCPP_INFO(this->get_logger(), "🔍 POSE_DEBUG: Exact match at queue start - Pose: [%.3f, %.3f, %.3f]",
                    out_pose.position.x, out_pose.position.y, out_pose.position.z);
        return true;
    }

    // Get bracketing poses
    const auto &p1 = *it;      // pose >= target_time
    const auto &p0 = *(it-1);  // pose < target_time

    double dt = p1.stamp.seconds() - p0.stamp.seconds();
    if (dt == 0) {
        out_pose = p0.pose;
        RCLCPP_INFO(this->get_logger(), "🔍 POSE_DEBUG: Zero time difference - Pose: [%.3f, %.3f, %.3f]",
                    out_pose.position.x, out_pose.position.y, out_pose.position.z);
        return true;
    }

    // Linear interpolation for position and SLERP for orientation
    // Use direct time calculation to avoid clock type mismatches
    double target_time_sec = stamp_to_double(target_stamp);
    double p0_time_sec = p0.stamp.seconds();
    double alpha = (target_time_sec - p0_time_sec) / dt;
    out_pose.position.x = p0.pose.position.x + alpha * (p1.pose.position.x - p0.pose.position.x);
    out_pose.position.y = p0.pose.position.y + alpha * (p1.pose.position.y - p0.pose.position.y);
    out_pose.position.z = p0.pose.position.z + alpha * (p1.pose.position.z - p0.pose.position.z);

    tf2::Quaternion q0, q1;
    tf2::fromMsg(p0.pose.orientation, q0);
    tf2::fromMsg(p1.pose.orientation, q1);
    tf2::Quaternion q_interp = q0.slerp(q1, alpha);
    out_pose.orientation = tf2::toMsg(q_interp);

    return true;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr LocalMapping::transform_pointcloud_to_map(
    const pcl::PointCloud<pcl::PointXYZ>::Ptr &cloud,
    const std::string &source_frame,
    const builtin_interfaces::msg::Time &stamp)
{
    if (!lidar_to_base_ready_) {
        RCLCPP_ERROR(this->get_logger(), "Transform failed: Lidar to base transform not ready");
        return nullptr;
    }

    // Build transform chain: lidar->base (static) then base->map (pose)
    tf2::Transform tf_lidar_base;
    tf2::fromMsg(lidar_to_base_tf_.transform, tf_lidar_base);

    geometry_msgs::msg::Pose interp_pose;
    if (!get_interpolated_pose(stamp, interp_pose)) {
        RCLCPP_ERROR(this->get_logger(), "Transform failed: Could not get interpolated pose for timestamp");
        return nullptr;
    }

    // Convert pose (base_link position in map) to transform (base->map)
    tf2::Transform tf_base_map;
    tf2::fromMsg(interp_pose, tf_base_map);

    // Correct transform composition: map_T_lidar = map_T_base * base_T_lidar
    tf2::Transform tf_lidar_map = tf_base_map * tf_lidar_base;

    // Debug transform with sample points
    RCLCPP_DEBUG(this->get_logger(), "Transforming %zu points from %s to map frame",
                 cloud->size(), source_frame.c_str());

    auto cloud_out = pcl::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    cloud_out->reserve(cloud->size());

    // Transform all points
    for (size_t i = 0; i < cloud->points.size(); ++i) {
        const auto &pt = cloud->points[i];
        tf2::Vector3 v(pt.x, pt.y, pt.z);
        tf2::Vector3 v_map = tf_lidar_map * v;
        cloud_out->points.emplace_back(v_map.x(), v_map.y(), v_map.z());
    }

    cloud_out->width = cloud_out->points.size();
    cloud_out->height = 1;
    cloud_out->is_dense = cloud->is_dense;


    return cloud_out;
}

pcl::PointCloud<pcl::PointXYZ>::Ptr LocalMapping::transform_pointcloud_to_map_with_pose(
    const pcl::PointCloud<pcl::PointXYZ>::Ptr &cloud,
    const geometry_msgs::msg::Pose &pose)
{
    if (!lidar_to_base_ready_) {
        RCLCPP_ERROR(this->get_logger(), "Transform failed: Lidar to base transform not ready");
        return nullptr;
    }

    // Build transform chain: lidar->base (static) then base->map (pose)
    tf2::Transform tf_lidar_base;
    tf2::fromMsg(lidar_to_base_tf_.transform, tf_lidar_base);

    // Convert pose (base_link position in map) to transform (base->map)
    tf2::Transform tf_base_map;
    tf2::fromMsg(pose, tf_base_map);

    // Correct transform composition: map_T_lidar = map_T_base * base_T_lidar
    tf2::Transform tf_lidar_map = tf_base_map * tf_lidar_base;

    // Debug transform with sample points
    RCLCPP_DEBUG(this->get_logger(), "Transforming %zu points using provided pose to map frame", cloud->size());

    auto cloud_out = pcl::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    cloud_out->reserve(cloud->size());

    // Transform all points
    for (size_t i = 0; i < cloud->points.size(); ++i) {
        const auto &pt = cloud->points[i];
        tf2::Vector3 v(pt.x, pt.y, pt.z);
        tf2::Vector3 v_map = tf_lidar_map * v;
        cloud_out->points.emplace_back(v_map.x(), v_map.y(), v_map.z());
    }

    cloud_out->width = cloud_out->points.size();
    cloud_out->height = 1;
    cloud_out->is_dense = cloud->is_dense;

    return cloud_out;
}

void LocalMapping::precompute_robot_radius_indices()
{
    robot_radius_indices_.clear();

    // Calculate robot radius in grid cells
    int radius_cells = static_cast<int>(std::ceil(robot_radius_ / map_resolution_));

    // Map center is at (map_width_/2, map_height_/2) since map is centered on radar_link
    int center_x = map_width_ / 2;
    int center_y = map_height_ / 2;

    // Pre-compute all indices within robot radius
    for (int dy = -radius_cells; dy <= radius_cells; ++dy) {
        for (int dx = -radius_cells; dx <= radius_cells; ++dx) {
            // Check if point is within robot radius
            double distance = std::sqrt(dx * dx + dy * dy) * map_resolution_;
            if (distance <= robot_radius_) {
                int x = center_x + dx;
                int y = center_y + dy;

                // Check bounds
                if (x >= 0 && x < map_width_ && y >= 0 && y < map_height_) {
                    int index = get_map_index(x, y);
                    robot_radius_indices_.push_back(index);
                }
            }
        }
    }

    RCLCPP_DEBUG(this->get_logger(), "Pre-computed %zu indices within robot radius %.2fm (radius_cells=%d)",
                robot_radius_indices_.size(), robot_radius_, radius_cells);
}

void LocalMapping::apply_robot_radius_free_area()
{
    // Apply to map_data_ (used for both 2D and 2.5D mapping)
    for (int index : robot_radius_indices_) {
        if (index >= 0 && index < static_cast<int>(map_data_.size())) {
            map_data_[index] = 0;  // Set to FREE
        }
    }
}

}  // namespace mapping_nodes

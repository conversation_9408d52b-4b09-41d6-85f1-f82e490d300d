import React, { useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import { Button, Select, Space, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import L from 'leaflet';
import type { Building, RobotState, Position, PointType } from '../types';
import { api } from '../services/api';

// 修复Leaflet默认图标问题
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const { Option } = Select;

interface MapComponentProps {
  building: Building;
  floorId: string;
  robotState: RobotState;
  onPointAdded: () => void;
}

// 安全的base64编码函数
const safeBtoa = (str: string) => {
  return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
    return String.fromCharCode(parseInt(p1, 16));
  }));
};

// 不同类型点位的图标颜色
const getPointIcon = (type: PointType) => {
  const colors = {
    pickup: '#52c41a',      // 绿色 - 取货点
    delivery: '#1890ff',    // 蓝色 - 卸货点
    elevator_call: '#fa8c16',  // 橙色 - 电梯呼叫点
    elevator_enter: '#722ed1'  // 紫色 - 电梯入口
  };
  
  return new L.Icon({
    iconUrl: `data:image/svg+xml;base64,${safeBtoa(`
      <svg width="25" height="41" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg">
        <path fill="${colors[type]}" d="M12.5 0C5.6 0 0 5.6 0 12.5c0 12.5 12.5 28.5 12.5 28.5S25 25 25 12.5C25 5.6 19.4 0 12.5 0z"/>
        <circle fill="white" cx="12.5" cy="12.5" r="6"/>
      </svg>
    `)}`,
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
  });
};

// 机器人图标
const robotIcon = new L.Icon({
  iconUrl: `data:image/svg+xml;base64,${safeBtoa(`
    <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
      <circle fill="#f5222d" cx="15" cy="15" r="12" stroke="white" stroke-width="2"/>
      <rect x="10" y="10" width="10" height="10" fill="white" rx="2"/>
      <circle cx="12" cy="13" r="1.5" fill="#f5222d"/>
      <circle cx="18" cy="13" r="1.5" fill="#f5222d"/>
      <rect x="13" y="16" width="4" height="1" fill="#f5222d" rx="0.5"/>
    </svg>
  `)}`,
  iconSize: [30, 30],
  iconAnchor: [15, 15],
});

// 地图点击事件组件
const MapClickHandler: React.FC<{
  onMapClick: (lat: number, lng: number) => void;
  addingPoint: boolean;
}> = ({ onMapClick, addingPoint }) => {
  useMapEvents({
    click: (e) => {
      if (addingPoint) {
        onMapClick(e.latlng.lat, e.latlng.lng);
      }
    },
  });
  return null;
};

export const MapComponent: React.FC<MapComponentProps> = ({
  building,
  floorId,
  robotState,
  onPointAdded
}) => {
  const [addingPoint, setAddingPoint] = useState(false);
  const [pointType, setPointType] = useState<PointType>('pickup');
  const mapRef = useRef<L.Map | null>(null);

  const floor = building.floors[floorId];
  if (!floor) return <div>楼层信息不存在</div>;

  // 获取所有点位
  const allPositions: (Position & { type: PointType })[] = [
    ...floor.pickup_positions.map(p => ({ ...p, type: 'pickup' as PointType })),
    ...floor.delivery_positions.map(p => ({ ...p, type: 'delivery' as PointType })),
    ...floor.elevator_call_positions.map(p => ({ ...p, type: 'elevator_call' as PointType })),
    ...floor.elevator_enter_positions.map(p => ({ ...p, type: 'elevator_enter' as PointType })),
  ];

  const handleMapClick = async (lat: number, lng: number) => {
    if (!addingPoint) return;

    try {
      // 将地图坐标转换为机器人坐标系
      // 这里假设地图坐标和机器人坐标系是1:1的关系
      // 实际使用时可能需要坐标变换
      const x = lng;
      const y = lat;

      const newPoint: Position = {
        id: `${pointType}_${Date.now()}`,
        name: `新${getPointTypeName(pointType)}`,
        x,
        y,
        theta: 0,
      };

      await api.addPoint(building.building_id, floorId, pointType, newPoint);
      onPointAdded();
      setAddingPoint(false);
      message.success(`添加${getPointTypeName(pointType)}成功`);
    } catch (error) {
      message.error('添加点位失败');
      console.error('Error adding point:', error);
    }
  };

  const getPointTypeName = (type: PointType): string => {
    const names = {
      pickup: '取货点',
      delivery: '卸货点',
      elevator_call: '电梯呼叫点',
      elevator_enter: '电梯入口'
    };
    return names[type];
  };

  const startAddingPoint = () => {
    setAddingPoint(true);
    message.info(`点击地图添加${getPointTypeName(pointType)}`);
  };

  const cancelAddingPoint = () => {
    setAddingPoint(false);
  };

  return (
    <div style={{ height: '400px', position: 'relative' }}>
      {/* 控制面板 */}
      <div style={{ 
        position: 'absolute', 
        top: '10px', 
        left: '10px', 
        zIndex: 1000,
        background: 'white',
        padding: '8px',
        borderRadius: '4px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space>
          <Select 
            value={pointType} 
            onChange={setPointType}
            style={{ width: 120 }}
            disabled={addingPoint}
          >
            <Option value="pickup">取货点</Option>
            <Option value="delivery">卸货点</Option>
            <Option value="elevator_call">电梯呼叫</Option>
            <Option value="elevator_enter">电梯入口</Option>
          </Select>
          
          {!addingPoint ? (
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={startAddingPoint}
            >
              添加点位
            </Button>
          ) : (
            <Button onClick={cancelAddingPoint}>
              取消添加
            </Button>
          )}
        </Space>
      </div>

      {/* 地图 */}
      <MapContainer
        center={[0, 0]} // 默认中心点
        zoom={2}
        style={{ height: '100%', width: '100%', cursor: addingPoint ? 'crosshair' : 'grab' }}
        ref={mapRef}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {/* 地图点击事件处理 */}
        <MapClickHandler onMapClick={handleMapClick} addingPoint={addingPoint} />
        
        {/* 机器人位置 */}
        <Marker 
          position={[robotState.position_y, robotState.position_x]} 
          icon={robotIcon}
        >
          <Popup>
            <div>
              <strong>机器人位置</strong><br />
              坐标: ({robotState.position_x.toFixed(2)}, {robotState.position_y.toFixed(2)})<br />
              速度: {Math.sqrt(robotState.velocity_x ** 2 + robotState.velocity_y ** 2).toFixed(2)} m/s
            </div>
          </Popup>
        </Marker>

        {/* 所有标记点 */}
        {allPositions.map((position) => (
          <Marker
            key={position.id}
            position={[position.y, position.x]}
            icon={getPointIcon(position.type)}
          >
            <Popup>
              <div>
                <strong>{position.name}</strong><br />
                类型: {getPointTypeName(position.type)}<br />
                坐标: ({position.x.toFixed(2)}, {position.y.toFixed(2)})<br />
                角度: {position.theta.toFixed(2)}°
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </div>
  );
};
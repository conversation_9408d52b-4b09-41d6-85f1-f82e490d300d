{"name": "ir100-simple-status-monitor", "version": "1.0.0", "description": "IR100机器人简化状态监控系统 - 只监控behaviour_status", "main": "simple_server.js", "scripts": {"start": "node simple_server.js", "dev": "node simple_server.js", "install-deps": "npm install", "ros-bridge": "python3 simple_ros_bridge.py", "check": "node -e \"console.log('✅ Node.js环境正常')\"", "test-python": "python3 -c \"import websockets; print('✅ Python websockets包正常')\""}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "ws": "^8.14.2", "cors": "^2.8.5"}, "devDependencies": {}, "keywords": ["ros2", "robot", "status", "monitor", "websocket", "behaviour"], "author": "IR100 Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "file://./ir100_ws/src/app"}, "homepage": "http://localhost:5000", "bugs": {"url": "file://./ir100_ws/src/app"}}
# IR100 机器人Web监控面板

IR100机器人的实时状态监控Web界面，支持HMS健康管理系统的完整数据展示。

## 📁 项目结构

```
/home/<USER>/ir100_ws/src/app/
├── frontend/                    # 前端页面
│   └── index.html              # 主监控界面
├── ros_bridge.py               # ROS与WebSocket桥接服务
├── web_server.js               # Web服务器
├── start.sh                    # 快速启动脚本
├── start_dashboard.sh          # 完整启动脚本
├── test_publisher.py           # 测试数据发布器
├── package.json                # 项目配置
└── README.md                   # 本文件
```

## 🚀 快速启动

### 方式一：快速启动
```bash
cd /home/<USER>/ir100_ws/src/app
./start.sh
```

### 方式二：详细启动
```bash
cd /home/<USER>/ir100_ws/src/app
./start_dashboard.sh
```

### 方式三：手动启动
```bash
# 终端1：启动ROS Bridge
cd /home/<USER>/ir100_ws/src/app
source /opt/ros/humble/setup.bash
python3 ros_bridge.py

# 终端2：启动Web服务器
cd /home/<USER>/ir100_ws/src/app
node web_server.js
```

## 🌐 访问地址

- **Web监控界面**: http://localhost:5000
- **API状态接口**: http://localhost:5000/api/status
- **健康检查接口**: http://localhost:5000/api/health

## 📊 功能特性

### 基础状态监控
- 🔗 ROS连接状态
- 🏃 当前运行模块
- ⚡ 系统健康状态
- 📊 模块运行状态

### HMS健康管理系统
- 🏥 HMS整体健康状态
- ⚠️ 活跃错误统计
- 📋 各模块健康状态详情
- 🚨 错误详细列表（错误码、模块、时间）

### 模块信息
- 📝 模块列表和状态
- ⏱️ 运行时长
- 🔄 实时状态更新

## 🔧 技术架构

### 后端组件
- **ROS Bridge** (`ros_bridge.py`): 订阅`/behaviour_status`话题，转发数据到WebSocket
- **Web Server** (`web_server.js`): Express服务器，提供REST API和Socket.IO实时通信

### 前端技术
- **纯HTML/CSS/JavaScript**: 无需构建，直接运行
- **Socket.IO**: 实时双向通信
- **响应式设计**: 支持桌面和移动设备

### 数据流
```
behaviour_status_publisher (C++) 
    ↓ /behaviour_status topic
ros_bridge.py 
    ↓ WebSocket (port 5001)
web_server.js 
    ↓ Socket.IO (port 5000)
frontend/index.html
```

## 📦 依赖要求

### 系统依赖
- ROS2 Humble
- Node.js ≥ 16.0
- Python3 ≥ 3.8

### Python包
- rclpy (ROS2)
- websockets
- json (内置)

### Node.js包
- express
- socket.io
- ws
- cors

## 🧪 测试和调试

### 测试数据发布
```bash
# 使用测试发布器模拟数据
python3 test_publisher.py
```

### 调试信息
- ROS Bridge日志: `logs/ros_bridge.log`
- Web服务器日志: `logs/web_server.log`
- 进程ID文件: `pids/`

## 🛠️ 配置选项

### 端口配置
- Web服务器: 5000 (可通过环境变量 `PORT` 修改)
- ROS WebSocket: 5001

### HMS数据格式
系统支持完整的HMS数据结构，包括：
- 整体健康状态 (HEALTHY/DEGRADED/UNHEALTHY/CRITICAL/FAILED)
- 错误统计 (按类别和严重程度)
- 模块健康状态
- 活跃错误详情

## 📝 API接口

### GET /api/status
返回完整的机器人状态信息

### GET /api/health
返回服务器健康检查信息

### WebSocket Events
- `behaviour_status`: 实时状态更新
- `request_status`: 请求状态刷新

## 🚨 故障排除

### 常见问题
1. **端口被占用**: 检查5000和5001端口是否被其他程序使用
2. **ROS话题无数据**: 确保behaviour_status_publisher正在运行
3. **WebSocket连接失败**: 检查防火墙设置

### 日志查看
```bash
tail -f logs/ros_bridge.log
tail -f logs/web_server.log
```

## 📄 许可证

MIT License - IR100 Team
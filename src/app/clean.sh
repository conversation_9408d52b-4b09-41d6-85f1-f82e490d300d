#!/bin/bash

# 清理临时文件和依赖

echo "Cleaning IR100 Web Dashboard..."

# 清理后端依赖
if [ -d "backend/node_modules" ]; then
    echo "Removing backend/node_modules..."
    rm -rf backend/node_modules
fi

if [ -f "backend/package-lock.json" ]; then
    echo "Removing backend/package-lock.json..."
    rm -f backend/package-lock.json
fi

# 清理前端依赖
if [ -d "frontend_backup/node_modules" ]; then
    echo "Removing frontend_backup/node_modules..."
    rm -rf frontend_backup/node_modules
fi

if [ -f "frontend_backup/package-lock.json" ]; then
    echo "Removing frontend_backup/package-lock.json..."
    rm -f frontend_backup/package-lock.json
fi

if [ -d "frontend_backup/dist" ]; then
    echo "Removing frontend_backup/dist..."
    rm -rf frontend_backup/dist
fi

# 清理根目录依赖
if [ -d "node_modules" ]; then
    echo "Removing root node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "Removing root package-lock.json..."
    rm -f package-lock.json
fi

# 清理日志文件
if [ -d "logs" ]; then
    echo "Removing logs directory..."
    rm -rf logs
fi

# 清理PID文件
if [ -f ".system_pids" ]; then
    echo "Removing .system_pids..."
    rm -f .system_pids
fi

# 清理Python缓存
find . -name "*.pyc" -delete 2>/dev/null
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null

echo "Cleanup complete!"
echo "Run 'npm install' in the required directories to reinstall dependencies."
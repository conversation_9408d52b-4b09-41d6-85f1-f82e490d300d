#include "behaviour_module/modules/press_floor_button_module.hpp"
#include <rclcpp/rclcpp.hpp>
#include <chrono>
#include "robot_msgs/srv/call_elevator.hpp"
#include "robot_msgs/srv/get_elevator_status.hpp"
#include "robot_msgs/srv/keep_door_open.hpp"

using namespace std::chrono_literals;

namespace behaviour_module {

// 轮询超时时间常量
constexpr double kPollTimeoutSeconds = 120.0;

PressFloorButtonModule::PressFloorButtonModule(
  const std::string & module_name,
  rclcpp::Node * node)
: BaseModule(module_name, node),
  robot_current_floor_(1),
  elevator_target_floor_(1),
  elevator_current_floor_(1),
  elevator_id_("48b02df90a3e")
{
  call_elevator_client_ = node_->create_client<robot_msgs::srv::CallElevator>("/elevator_control/call_elevator");
  get_status_client_  = node_->create_client<robot_msgs::srv::GetElevatorStatus>("/elevator_control/get_elevator_status");
  keep_door_open_client_ = node_->create_client<robot_msgs::srv::KeepDoorOpen>("/elevator_control/keep_door_open");

  RCLCPP_INFO(node_->get_logger(), "云端召梯模式模块已初始化");
}

void PressFloorButtonModule::onTaskCancel()
{
  RCLCPP_INFO(node_->get_logger(), "云端召梯操作已取消");
  if (status_timer_) {
    status_timer_->cancel();
    status_timer_.reset();
  }
}

void PressFloorButtonModule::onTimer()
{
  // 此模块使用自己的定时器进行轮询，基类的onTimer可以为空实现
  // 或者可以在这里添加一些调试信息
  if (enable_debug_logging_) {
    RCLCPP_DEBUG(node_->get_logger(), "PressFloorButtonModule onTimer called");
  }
}

void PressFloorButtonModule::onTaskStart(
  const std::string & task_id,
  const SchedulerTaskParameters & parameters)
{
  // 从参数获取当前楼层和目标楼层
  robot_current_floor_ = parameters.current_floor;
  elevator_target_floor_  = parameters.target_floor;
  
  // 从参数获取电梯ID，如果没有则使用默认值
  if (!parameters.elevator_id.empty()) {
    elevator_id_ = parameters.elevator_id;
  }
  
  RCLCPP_INFO(node_->get_logger(),
              "开始呼叫电梯，当前楼层=%d，目标楼层=%d，电梯ID=%s",
              robot_current_floor_, elevator_target_floor_, elevator_id_.c_str());

  // 记录轮询起点时间
  poll_start_time_ = node_->now();

  auto request = std::make_shared<robot_msgs::srv::CallElevator::Request>();
  request->elevator_id  = elevator_id_;
  request->target_floor = std::to_string(robot_current_floor_);  // 转换为字符串类型

  if (!call_elevator_client_->wait_for_service(2s)) {
    reportFailure("call_elevator服务不可用");
    return;
  }

  call_elevator_client_->async_send_request(request,
    [this](rclcpp::Client<robot_msgs::srv::CallElevator>::SharedFuture future) {
      auto resp = future.get();
      if (resp->success) {
        RCLCPP_INFO(node_->get_logger(), "电梯呼叫成功，开始轮询电梯状态...");
        status_timer_ = node_->create_wall_timer(
          2s,
          [this]() { this->checkElevatorStatus(); }
        );
      } else {
        reportFailure("电梯呼叫失败");
      }
    }
  );
}

void PressFloorButtonModule::checkElevatorStatus()
{
  // 超时检测
  if ((node_->now() - poll_start_time_).seconds() > kPollTimeoutSeconds) {
    RCLCPP_ERROR(node_->get_logger(), "电梯到达轮询超时");
    if (status_timer_) {
      status_timer_->cancel();
      status_timer_.reset();
    }
    reportFailure("Elevator arrival timeout");
    return;
  }

  auto request = std::make_shared<robot_msgs::srv::GetElevatorStatus::Request>();
  request->elevator_id = elevator_id_;

  if (!get_status_client_->wait_for_service(1s)) {
    RCLCPP_WARN(node_->get_logger(), "get_elevator_status服务不可用");
    return;
  }

  get_status_client_->async_send_request(request,
    [this](rclcpp::Client<robot_msgs::srv::GetElevatorStatus>::SharedFuture future) {
      try {
        auto resp = future.get();
        if (!resp->success) {
          RCLCPP_ERROR(node_->get_logger(), "查询电梯状态失败");
          if (status_timer_) {
            status_timer_->cancel();
            status_timer_.reset();
          }
          reportFailure("get_elevator_status returned false");
          return;
        }
        elevator_current_floor_ = resp->current_floor;
        RCLCPP_DEBUG(node_->get_logger(),
                     "当前楼层 %d，门 %s，继续轮询...",
                     elevator_current_floor_,
                     resp->door_opened ? "开" : "关");
        if (elevator_current_floor_ == robot_current_floor_ && resp->door_opened) {
          RCLCPP_INFO(node_->get_logger(), "电梯已到达机器人所在楼层且开门，调用keep_door_open...");
          callKeepDoorOpen();
        }
      } catch (const std::exception & e) {
        RCLCPP_ERROR(node_->get_logger(), "查询服务异常: %s", e.what());
        if (status_timer_) {
          status_timer_->cancel();
          status_timer_.reset();
        }
        reportFailure("get_elevator_status exception");
      }
    }
  );
}

void PressFloorButtonModule::callKeepDoorOpen()
{
  if (status_timer_) {
    status_timer_->cancel();
    status_timer_.reset();
  }

  auto request = std::make_shared<robot_msgs::srv::KeepDoorOpen::Request>();
  request->elevator_id = elevator_id_;

  if (!keep_door_open_client_->wait_for_service(1s)) {
    reportFailure("keep_door_open服务不可用");
    return;
  }

  keep_door_open_client_->async_send_request(request,
    [this](rclcpp::Client<robot_msgs::srv::KeepDoorOpen>::SharedFuture future) {
      auto resp = future.get();
      if (resp->success) {
        reportSuccess("电梯门已保持开启，任务完成");
      } else {
        reportFailure("保持开门失败");
      }
    }
  );
}

}  // namespace behaviour_module

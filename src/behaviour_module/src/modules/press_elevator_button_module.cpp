#include "behaviour_module/modules/press_elevator_button_module.hpp"
#include <rclcpp/rclcpp.hpp>
#include <chrono>
#include <thread>

using namespace std::chrono_literals;

namespace behaviour_module
{

PressElevatorButtonModule::PressElevatorButtonModule(const std::string & module_name, rclcpp::Node* node)
: BaseModule(module_name, node),
  elevator_detected_(false),
  button_detected_(false),
  button_pressed_(false),
  button_press_verified_(false),
  api_mode_(false),
  elevator_state_(ElevatorControlState::IDLE),
  approach_distance_(1.0),
  button_press_force_(5.0),
  max_button_press_attempts_(3)
{
  // 创建订阅器
  range_sensor_subscription_ = node_->create_subscription<sensor_msgs::msg::Range>(
    "range_sensor", 10,
    std::bind(&PressElevatorButtonModule::rangeSensorCallback, this, std::placeholders::_1));
  
  // 创建电梯状态订阅器
  elevator_status_subscription_ = node_->create_subscription<std_msgs::msg::String>(
    "elevator_control/elevator_status", 10,
    std::bind(&PressElevatorButtonModule::elevatorStatusCallback, this, std::placeholders::_1));

  // 创建电梯控制服务客户端
  call_elevator_client_ = node_->create_client<robot_msgs::srv::CallElevator>("/elevator_control/call_elevator");
  get_status_client_ = node_->create_client<robot_msgs::srv::GetElevatorStatus>("/elevator_control/get_elevator_status");
  keep_door_open_client_ = node_->create_client<robot_msgs::srv::KeepDoorOpen>("/elevator_control/keep_door_open");

  RCLCPP_INFO(node_->get_logger(), "按电梯按钮模块已初始化");
}


void PressElevatorButtonModule::onTaskCancel()
{
  RCLCPP_INFO(node_->get_logger(), "按电梯按钮操作已取消");
}

void PressElevatorButtonModule::onTimer()
{
  // 检查各个步骤的完成状态
  if (!elevator_detected_) {
    // 继续接近电梯
    return;
  }
  
  if (!button_detected_) {
    detectElevatorButton();
    return;
  }
  
  if (!button_pressed_) {
    pressElevatorButton();
    return;
  }
  
  if (!button_press_verified_) {
    verifyButtonPressed();
    return;
  }
  
  // 所有步骤完成
  reportSuccess("电梯按钮按下完成");
}

void PressElevatorButtonModule::approachElevator()
{
  reportProgress("接近电梯");
  
  // 模拟接近电梯
  std::this_thread::sleep_for(3s);
  
  elevator_detected_ = true;
  RCLCPP_INFO(node_->get_logger(), "已接近电梯");
}

void PressElevatorButtonModule::detectElevatorButton()
{
  reportProgress("检测电梯按钮");
  
  // 模拟按钮检测
  std::this_thread::sleep_for(2s);
  
  button_detected_ = true;
  RCLCPP_INFO(node_->get_logger(), "电梯按钮检测完成");
}

void PressElevatorButtonModule::pressElevatorButton()
{
  reportProgress("按下电梯按钮");
  
  // 模拟按钮按下
  std::this_thread::sleep_for(1s);
  
  button_pressed_ = true;
  RCLCPP_INFO(node_->get_logger(), "电梯按钮已按下");
}

void PressElevatorButtonModule::verifyButtonPressed()
{
  reportProgress("验证按钮按下状态");
  
  // 模拟验证
  std::this_thread::sleep_for(1s);
  
  button_press_verified_ = true;
  RCLCPP_INFO(node_->get_logger(), "电梯按钮按下状态已验证");
}

void PressElevatorButtonModule::rangeSensorCallback(const sensor_msgs::msg::Range::SharedPtr msg)
{
  // 处理距离传感器数据
  if (enable_debug_logging_) {
    RCLCPP_DEBUG(node_->get_logger(), "距离传感器读数: %.3f", msg->range);
  }
}

void PressElevatorButtonModule::elevatorStatusCallback(const std_msgs::msg::String::SharedPtr msg)
{
  // 处理电梯状态信息
  if (enable_debug_logging_) {
    RCLCPP_DEBUG(node_->get_logger(), "电梯状态更新: %s", msg->data.c_str());
  }
}

void PressElevatorButtonModule::startElevatorAPIMode(const SchedulerTaskParameters & parameters)
{
  RCLCPP_INFO(node_->get_logger(), "启动电梯API模式 - 机器人在电梯内呼叫到目标楼层");
  api_mode_ = true;
  elevator_state_ = ElevatorControlState::CALLING_ELEVATOR;
  current_elevator_id_ = parameters.elevator_id;
  call_start_time_ = std::chrono::steady_clock::now();
  
  // 调用电梯API - 呼叫到目标楼层，不是机器人当前楼层
  callElevatorAPI(parameters.elevator_id, std::to_string(parameters.target_floor));
}

void PressElevatorButtonModule::startPhysicalMode()
{
  RCLCPP_INFO(node_->get_logger(), "启动物理按键模式");
  api_mode_ = false;
  
  // 开始模拟流程
  approachElevator();
}

void PressElevatorButtonModule::callElevatorAPI(const std::string& elevator_id, const std::string& target_floor)
{
  auto request = std::make_shared<robot_msgs::srv::CallElevator::Request>();
  request->elevator_id = elevator_id;
  request->target_floor = target_floor;
  
  RCLCPP_INFO(node_->get_logger(), "调用电梯API: 电梯ID=%s, 目标楼层=%s", 
              elevator_id.c_str(), target_floor.c_str());
  
  // 使用异步回调方式，避免executor冲突
  auto callback = [this](rclcpp::Client<robot_msgs::srv::CallElevator>::SharedFuture future) {
    try {
      auto result = future.get();
      handleElevatorCallResponse(result);
    } catch (const std::exception& e) {
      RCLCPP_ERROR(node_->get_logger(), "电梯召唤异常: %s", e.what());
      elevator_state_ = ElevatorControlState::FAILED;
      reportFailure("电梯召唤异常");
    }
  };
  
  // 直接异步调用，不做同步检查
  call_elevator_client_->async_send_request(request, callback);
}

void PressElevatorButtonModule::handleElevatorCallResponse(const robot_msgs::srv::CallElevator::Response::SharedPtr response)
{
  if (response->success) {
    RCLCPP_INFO(node_->get_logger(), "电梯召唤成功，开始状态轮询");
    elevator_state_ = ElevatorControlState::WAITING_FOR_ARRIVAL;
    status_check_start_time_ = std::chrono::steady_clock::now();
    startStatusPolling();
  } else {
    RCLCPP_ERROR(node_->get_logger(), "电梯召唤失败: %s", response->message.c_str());
    elevator_state_ = ElevatorControlState::FAILED;
    reportFailure("电梯召唤失败: " + response->message);
  }
}

void PressElevatorButtonModule::startStatusPolling()
{
  // 创建定时器，每2秒检查一次电梯状态
  status_check_timer_ = node_->create_wall_timer(
    std::chrono::seconds(2),
    [this]() { checkElevatorStatusTimer(); }
  );
  
  RCLCPP_INFO(node_->get_logger(), "开始定时检查电梯状态");
}

void PressElevatorButtonModule::stopStatusPolling()
{
  if (status_check_timer_) {
    status_check_timer_->cancel();
    status_check_timer_.reset();
    RCLCPP_INFO(node_->get_logger(), "停止电梯状态检查");
  }
}

void PressElevatorButtonModule::checkElevatorStatusTimer()
{
  // 检查超时
  auto now = std::chrono::steady_clock::now();
  auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - status_check_start_time_).count();
  
  if (elapsed > current_parameters_.door_wait_timeout) {
    RCLCPP_ERROR(node_->get_logger(), "等待电梯超时 (%ld秒)", elapsed);
    elevator_state_ = ElevatorControlState::FAILED;
    stopStatusPolling();
    reportFailure("等待电梯超时");
    return;
  }
  
  // 发送状态查询请求（直接异步调用，不做同步检查）
  auto request = std::make_shared<robot_msgs::srv::GetElevatorStatus::Request>();
  request->elevator_id = current_elevator_id_;
  
  auto callback = [this](rclcpp::Client<robot_msgs::srv::GetElevatorStatus>::SharedFuture future) {
    try {
      auto result = future.get();
      handleElevatorStatusResponse(result);
    } catch (const std::exception& e) {
      RCLCPP_WARN(node_->get_logger(), "电梯状态查询异常: %s，继续尝试...", e.what());
    }
  };
  
  get_status_client_->async_send_request(request, callback);
}

void PressElevatorButtonModule::handleElevatorStatusResponse(const robot_msgs::srv::GetElevatorStatus::Response::SharedPtr response)
{
  if (!response->success) {
    RCLCPP_WARN(node_->get_logger(), "电梯状态查询失败: %s，继续尝试...", response->message.c_str());
    return;
  }
  
  RCLCPP_INFO(node_->get_logger(), "电梯状态: 楼层=%d, 门状态=%s, 上行=%s, 下行=%s", 
              response->current_floor, 
              response->door_opened ? "开" : "关",
              response->moving_up ? "是" : "否",
              response->moving_down ? "是" : "否");
  
  // 检查电梯是否到达目标楼层并且门已打开
  if (response->current_floor == current_parameters_.target_floor && response->door_opened) {
    RCLCPP_INFO(node_->get_logger(), "电梯已到达目标楼层并门已打开！");
    elevator_state_ = ElevatorControlState::COMPLETED;
    stopStatusPolling();
    callKeepDoorOpen();  // 调用保持开门服务
  } else if (response->current_floor == current_parameters_.target_floor) {
    RCLCPP_INFO(node_->get_logger(), "电梯已到达目标楼层，等待门打开...");
  } else {
    RCLCPP_INFO(node_->get_logger(), "电梯正在移动中，当前楼层: %d, 目标楼层: %d", 
                response->current_floor, current_parameters_.target_floor);
  }
}



void PressElevatorButtonModule::onTaskStart(const std::string & task_id, const SchedulerTaskParameters & parameters)
{
  RCLCPP_INFO(node_->get_logger(), "开始按电梯按钮，任务: %s, 执行模式: %s, 电梯ID: %s",
              task_id.c_str(), parameters.execution_mode.c_str(), parameters.elevator_id.c_str());

  // 保存当前参数
  current_parameters_ = parameters;

  // 验证API模式下的电梯ID参数
  if (parameters.execution_mode == "api" && parameters.elevator_id.empty()) {
    RCLCPP_ERROR(node_->get_logger(), "API模式需要提供elevator_id参数");
    reportFailure("API模式需要提供elevator_id参数");
    return;
  }

  // 根据执行模式选择不同的处理方式
  if (parameters.execution_mode == "api") {
    startElevatorAPIMode(parameters);
  } else {
    startPhysicalMode();
  }
}

void PressElevatorButtonModule::callKeepDoorOpen()
{
  auto request = std::make_shared<robot_msgs::srv::KeepDoorOpen::Request>();
  request->elevator_id = current_elevator_id_;

  if (!keep_door_open_client_->wait_for_service(1s)) {
    reportFailure("keep_door_open服务不可用");
    return;
  }

  keep_door_open_client_->async_send_request(request,
    [this](rclcpp::Client<robot_msgs::srv::KeepDoorOpen>::SharedFuture future) {
      auto resp = future.get();
      if (resp->success) {
        reportSuccess("电梯门已保持开启，任务完成");
      } else {
        reportFailure("保持开门失败");
      }
    }
  );
}

}  // namespace behaviour_module

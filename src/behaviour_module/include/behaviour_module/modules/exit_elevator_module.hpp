#ifndef BEHAVIOUR_MODULE_EXIT_ELEVATOR_MODULE_HPP
#define BEHAVIOUR_MODULE_EXIT_ELEVATOR_MODULE_HPP

#include "behaviour_module/modules/base_module.hpp"
#include "behaviour_module/task_parameters.hpp"
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/int32.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <nav2_msgs/action/navigate_to_pose.hpp>
#include "robot_msgs/srv/get_elevator_status.hpp"
#include <chrono>
#include <memory>

namespace behaviour_module
{

class ExitElevatorModule : public BaseModule
{
public:
  explicit ExitElevatorModule(const std::string & module_name, rclcpp::Node* node);

protected:
  // 实现基类虚函数
  void onTaskStart(const std::string & task_id, const SchedulerTaskParameters & parameters) override;
  void onTaskCancel() override;
  void onTimer() override;

private:
  // 离开电梯的具体步骤
  void waitForTargetFloor();
  void waitForDoorOpen();
  void waitForElevatorDoor();
  void exitElevator();
  void verifyExitedElevator();
  void verifyOutsideElevator();
  void queryElevatorStatus();

  // 全向轮控制方法
  void moveOutWithOmniWheels();
  void moveForwardSlowly();
  void stopRobot();
  void adjustPositionLaterally(double lateral_offset);
  void moveToExactPosition(double target_x, double target_y);
  bool isExitedByLaser();
  bool isOutsideElevatorByLaser();
  void moveToElevatorWithOmniWheels();
  double getCurrentYaw();

  // 导航相关方法
  bool calculateElevatorExternalPose(geometry_msgs::msg::PoseStamped& target_pose);
  void sendNavigationGoal(const geometry_msgs::msg::PoseStamped& target_pose);

  // 检查和超时方法
  bool checkTimeout();

  // 资源管理和线程安全方法
  void cleanupAllResources();
  void cleanupMoveTimer();
  void cleanupStatusQueryTimer();
  void resetStateVariables();

  // 回调函数
  void floorIndicatorCallback(const std_msgs::msg::Int32::SharedPtr msg);
  void positionCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
  void laserCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg);

  // 订阅器
  rclcpp::Subscription<std_msgs::msg::Int32>::SharedPtr floor_indicator_subscription_;
  rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr position_subscription_;
  rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr laser_subscription_;

  // 发布器
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_publisher_;

  // 电梯状态查询服务客户端
  rclcpp::Client<robot_msgs::srv::GetElevatorStatus>::SharedPtr get_elevator_status_client_;

  // 导航动作客户端
  rclcpp_action::Client<nav2_msgs::action::NavigateToPose>::SharedPtr nav_client_;
  rclcpp_action::ClientGoalHandle<nav2_msgs::action::NavigateToPose>::SharedPtr nav_goal_handle_;

  // 定时器
  rclcpp::TimerBase::SharedPtr move_timer_;
  rclcpp::TimerBase::SharedPtr status_query_timer_;

  // 状态变量
  int target_floor_;
  int current_floor_;
  bool target_floor_reached_;
  bool door_open_;
  bool successfully_exited_;
  bool outside_elevator_;
  bool position_verified_;

  // 当前传感器数据
  std::shared_ptr<geometry_msgs::msg::PoseStamped> current_robot_pose_;
  std::shared_ptr<sensor_msgs::msg::LaserScan> current_laser_scan_;

  // 配置参数
  double door_wait_timeout_;
  double exit_speed_;
  std::chrono::steady_clock::time_point door_wait_start_time_;
  std::chrono::steady_clock::time_point verification_start_time_;
  int exit_attempt_count_;
  int max_exit_attempts_;
  int no_pose_count_;  // 无位置信息计数器（替代静态变量）

  // 任务参数
  SchedulerTaskParameters current_parameters_;
};

} // namespace behaviour_module

#endif // BEHAVIOUR_MODULE_EXIT_ELEVATOR_MODULE_HPP 
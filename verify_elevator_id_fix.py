#!/usr/bin/env python3
"""
验证电梯ID传参修复的脚本
检查代码中是否正确处理了电梯ID参数
"""

import os
import re

def check_file_for_elevator_id_handling(file_path):
    """检查文件中的电梯ID处理"""
    print(f"\n=== 检查文件: {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    fixes = []
    
    # 检查onTaskStart方法是否有execution_mode检查
    if 'onTaskStart' in content:
        if 'execution_mode' in content and 'elevator_id' in content:
            fixes.append("✓ onTaskStart方法包含execution_mode和elevator_id处理")
        else:
            issues.append("✗ onTaskStart方法缺少execution_mode或elevator_id处理")
    
    # 检查queryElevatorStatus方法是否正确处理电梯ID
    if 'queryElevatorStatus' in content:
        if 'current_parameters_.elevator_id.empty()' in content:
            if 'RCLCPP_DEBUG' in content and '使用外部传入的电梯ID' in content:
                fixes.append("✓ queryElevatorStatus方法正确处理外部电梯ID")
            else:
                issues.append("✗ queryElevatorStatus方法处理电梯ID但缺少调试日志")
        else:
            issues.append("✗ queryElevatorStatus方法未正确处理电梯ID")
    
    # 检查是否有API模式验证
    if 'API模式需要提供elevator_id参数' in content:
        fixes.append("✓ 包含API模式电梯ID验证")
    else:
        if 'execution_mode' in content and 'api' in content:
            issues.append("✗ 有API模式处理但缺少电梯ID验证")
    
    # 输出结果
    if fixes:
        print("修复项:")
        for fix in fixes:
            print(f"  {fix}")
    
    if issues:
        print("问题项:")
        for issue in issues:
            print(f"  {issue}")
    
    return len(issues) == 0

def main():
    """主函数"""
    print("验证电梯ID传参修复")
    print("=" * 50)
    
    # 要检查的文件列表
    files_to_check = [
        "src/behaviour_module/src/modules/enter_elevator_module.cpp",
        "src/behaviour_module/src/modules/exit_elevator_module.cpp",
        "src/behaviour_module/src/modules/press_elevator_button_module.cpp",
        "src/behaviour_module/src/modules/press_floor_button_module.cpp"
    ]
    
    all_good = True
    
    for file_path in files_to_check:
        is_good = check_file_for_elevator_id_handling(file_path)
        all_good = all_good and is_good
    
    print("\n" + "=" * 50)
    if all_good:
        print("✓ 所有检查通过！电梯ID传参修复完成。")
    else:
        print("✗ 发现问题，需要进一步修复。")
    
    print("\n修复总结:")
    print("1. 进入电梯模块: 添加了execution_mode检查和电梯ID验证")
    print("2. 退出电梯模块: 添加了execution_mode检查和电梯ID验证")
    print("3. 两个模块的queryElevatorStatus方法都改进了电梯ID处理逻辑")
    print("4. 添加了调试日志来跟踪电梯ID的使用")
    
    print("\n对比呼叫电梯模块:")
    print("- 呼叫电梯模块已经正确处理了电梯ID参数")
    print("- 现在进入和退出电梯模块也采用了相同的处理方式")

if __name__ == '__main__':
    main()
